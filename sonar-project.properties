sonar.organization=conari-app
sonar.projectKey=conari-app_backend

sonar.php.coverage.reportPaths=coverage.xml

# Path to the parent source code directory. Such as:
sonar.sources=app
# Language: check for specific language file, such as for PHP
sonar.language=php
# if you want to analyze both PHP and JavaScript, comment this variable
# Encoding of the source code
sonar.sourceEncoding=UTF-8
# you can exclude all the folders that you don’t want to analyze.
# Such as, I’m excluding these directories
sonar.exclusions=app/Providers/**, vendor/**, node_modules/**, docker-conf/**, lang/**, bootstrap/**, config/*, storage/**, public/**, tests/**
