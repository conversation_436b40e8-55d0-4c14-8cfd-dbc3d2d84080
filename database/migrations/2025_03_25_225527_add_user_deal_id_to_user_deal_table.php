<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_deal', function (Blueprint $table) {
            $table->after('reserve_slot', function (Blueprint $table) {
                $table->foreignId('user_deal_id')->nullable();
            });
        });
    }

    public function down(): void
    {
        Schema::table('user_deal', function (Blueprint $table) {
            $table->dropColumn([
                'user_deal_id',
            ]);
        });
    }
};
