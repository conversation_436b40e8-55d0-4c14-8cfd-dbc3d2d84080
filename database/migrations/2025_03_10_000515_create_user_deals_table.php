<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_deal', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('deal_id');
            $table->dateTime('reserved_at')->nullable();
            $table->dateTime('redeemed_at')->nullable();
            $table->dateTime('reuse_after')->nullable();
            $table->enum('status', ['available', 'redeemable', 'pending'])->default('available');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_deals');
    }
};
