<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_deal', function (Blueprint $table) {
            $table->enum('status', ['available', 'redeemable', 'pending', 'rereservable', 'upcoming'])->default('available')->change();
        });
    }

    public function down(): void
    {
        Schema::table('user_deal', function (Blueprint $table) {
            $table->enum('status', ['available', 'redeemable', 'pending'])->default('available')->change();
        });
    }
};
