<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('partner_locations', function (Blueprint $table) {
            $table->after('partner_id', function (Blueprint $table) {
                $table->foreignId('admin_id')->nullable();
            });
        });
    }

    public function down(): void
    {
        Schema::table('partner_locations', function (Blueprint $table) {
            $table->dropColumn([
                'admin_id',
            ]);
        });
    }
};
