<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('invitations', function (Blueprint $table) {
            // Add fields for user-to-user invitations
            $table->foreignId('inviter_user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('invitee_user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->string('token')->nullable()->unique();
            $table->timestamp('token_expires_at')->nullable();
            $table->boolean('is_used')->default(false);

            // Add index for better performance
            $table->index(['token', 'is_used']);
            $table->index(['inviter_user_id']);
            $table->index(['invitee_user_id']);
        });
    }

    public function down(): void
    {
        Schema::table('invitations', function (Blueprint $table) {
            $table->dropForeign(['inviter_user_id']);
            $table->dropForeign(['invitee_user_id']);
            $table->dropIndex(['token', 'is_used']);
            $table->dropIndex(['inviter_user_id']);
            $table->dropIndex(['invitee_user_id']);
            $table->dropColumn([
                'inviter_user_id',
                'invitee_user_id',
                'token',
                'token_expires_at',
                'is_used',
            ]);
        });
    }
};
