<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_deal', function (Blueprint $table) {
            $table->after('reuse_after', function (Blueprint $table) {
                $table->string('reserve_slot')->nullable();
            });
        });
    }

    public function down(): void
    {
        Schema::table('user_deal', function (Blueprint $table) {
            $table->dropColumn([
                'reserve_slot',
            ]);
        });
    }
};
