<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('deal_slots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('deal_id');
            $table->integer('day');
            $table->time('from');
            $table->time('to');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('deal_slots');
    }
};
