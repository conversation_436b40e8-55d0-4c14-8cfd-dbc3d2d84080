<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reels', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable();
            $table->string('caption')->nullable();
            $table->string('url');
            $table->foreignId('partner_id');
            $table->timestamps();
        });

        Schema::create('reel_location', function (Blueprint $table) {
            $table->id('pivot_id');
            $table->foreignId('reel_id')->constrained('reels');
            $table->foreignId('partner_location_id')->constrained('partner_locations');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('reels');
        Schema::dropIfExists('reel_location');
    }
};
