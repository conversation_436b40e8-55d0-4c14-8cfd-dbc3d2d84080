<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('deal_slots', function (Blueprint $table) {
            $table->after('to', function (Blueprint $table) {
                $table->integer('available_seats')->default(0);
            });
        });
    }

    public function down(): void
    {
        Schema::table('deal_slots', function (Blueprint $table) {
            $table->dropColumn([
                'available_seats',
            ]);
        });
    }
};
