<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('deals', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable();
            $table->string('description')->nullable();
            $table->decimal('max_saving')->default(0);
            $table->integer('max_usage_per_day')->default(1);
            $table->integer('reuse_limit_days')->default(0);
            $table->string('valid_days')->nullable();
            $table->foreignId('partner_location_id');
            $table->string('deal_type');
            $table->string('tags')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('deals');
    }
};
