<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('partner_locations', function (Blueprint $table) {
            $table->after('name', function (Blueprint $table) {
                $table->decimal('price_per_person', 8, 2)->nullable();
            });
        });
    }

    public function down(): void
    {
        Schema::table('partner_locations', function (Blueprint $table) {
            $table->dropColumn([
                'price_per_person',
            ]);
        });
    }
};
