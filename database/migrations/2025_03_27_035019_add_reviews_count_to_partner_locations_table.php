<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('partner_locations', function (Blueprint $table) {
            $table->renameColumn('self_rate', 'reviews_count');
        });

        Schema::table('partner_locations', function (Blueprint $table) {
            $table->integer('reviews_count')->change();
            $table->text('opening_hours')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('partner_locations', function (Blueprint $table) {
            $table->renameColumn('reviews_count', 'self_rate');
        });

        Schema::table('partner_locations', function (Blueprint $table) {
            $table->float('self_rate')->change();
            $table->string('opening_hours')->nullable()->change();
        });
    }
};
