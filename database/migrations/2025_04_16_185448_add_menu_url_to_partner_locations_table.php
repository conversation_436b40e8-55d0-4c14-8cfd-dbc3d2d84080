<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('partner_locations', function (Blueprint $table) {
            $table->string('menu_url')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('partner_locations', function (Blueprint $table) {
            $table->dropColumn('menu_url');
        });
    }
};
