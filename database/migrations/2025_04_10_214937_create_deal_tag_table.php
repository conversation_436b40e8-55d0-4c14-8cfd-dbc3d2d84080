<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $options = [
            '2-for-1_main_courses' => \App\Enums\DealType::TWO_FOR_ONE->value,
            'free_item_with_purchase' => \App\Enums\DealType::FREE_ITEM_WITH_PURCHASE->value,
            'fixed_amount_off' => \App\Enums\DealType::AED_40_DISCOUNT->value,
            'aed_1_for_an_item' => \App\Enums\DealType::ONE_AED_SPECIAL->value,
        ];

        foreach ($options as $oldOption => $option) {
            DB::table('deals')->where('deal_type', $oldOption)->update([
                'deal_type' => $option,
            ]);
        }

        Schema::create('deal_tag', function (Blueprint $table) {
            $table->id();
            $table->foreignId('deal_id')->constrained('deals')->cascadeOnDelete();
            $table->foreignId('tag_id')->constrained('tags')->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('deal_tag');

        $options = [
            \App\Enums\DealType::TWO_FOR_ONE->value => '2-for-1_main_courses',
            \App\Enums\DealType::FREE_ITEM_WITH_PURCHASE->value => 'free_item_with_purchase',
            \App\Enums\DealType::AED_40_DISCOUNT->value => 'fixed_amount_off',
            \App\Enums\DealType::ONE_AED_SPECIAL->value => 'aed_1_for_an_item',
        ];

        foreach ($options as $oldOption => $option) {
            DB::table('deals')->where('deal_type', $oldOption)->update([
                'deal_type' => $option,
            ]);
        }
    }
};
