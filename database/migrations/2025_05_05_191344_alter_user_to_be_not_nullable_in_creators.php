<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        \App\Models\Creator::query()->get()->each(function (\App\Models\Creator $creator) {
            /** @var ?\App\Models\User $user */
            $user = $creator->user;

            $creator->name = $user->name ?? 'NoName';
            $creator->save();
        });

        Schema::table('creators', function (Blueprint $table) {
            $table->string('name')->nullable(false)->change();
        });
    }

    public function down(): void
    {
        Schema::table('creators', function (Blueprint $table) {
            $table->string('name')->nullable(true)->change();
        });
    }
};
