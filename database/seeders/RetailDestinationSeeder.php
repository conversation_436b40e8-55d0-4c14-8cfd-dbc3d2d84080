<?php

namespace Database\Seeders;

use App\Models\RetailDestination;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class RetailDestinationSeeder extends Seeder
{
    public function run(): void
    {
        $retailDestinations = [
            'The Dubai Mall',
            'Mall of the Emirates',
            'Dubai Festival City Mall',
            'BurJuman Centre',
            'Mirdif City Centre',
            'Dubai Outlet Mall',
            'Dubai Marina Mall',
            'City Walk',
            'Alserkal Avenue',
        ];

        foreach ($retailDestinations as $retailDestination) {
            RetailDestination::query()->firstOrCreate([
                'name' => $retailDestination,
                'slug' => Str::slug($retailDestination),
            ]);
        }
    }
}
