<?php

namespace Database\Seeders;

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\Reel;
use App\Models\Tag;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Seeder;

class PartnerSeeder extends Seeder
{
    public function run(): void
    {
        $partners = Partner::factory()
            ->has(
                PartnerLocation::factory()
                    ->has(Reel::factory()->count(2), 'reels')
                    ->has(
                        Deal::factory()->has(
                            DealSlot::factory()->count(3),
                            'dealSlots'
                        )->count(2),
                        'deals'
                    ),
                'locations'
            )
            ->count(10)
            ->create();

        $tags = Tag::query()->pluck('id')->toArray();

        foreach ($partners as $partner) {
            /** @var Partner $partner */
            /** @var Collection<PartnerLocation> $locations */
            $locations = $partner->locations;
            foreach ($locations as $location) {
                $location->tags()->attach(
                    \Arr::random($tags, min(3, fake()->randomNumber(2)))
                );
            }
        }
    }
}
