<?php

namespace Database\Seeders;

use App\Models\Area;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class AreaSeeder extends Seeder
{
    public function run(): void
    {
        $areas = [
            'Downtown Dubai',
            'Business Bay',
            'Dubai Marina',
            'Jumeirah Beach Residence (JBR)',
            'Palm Jumeirah',
            'Bur Dubai',
            'Deira',
            'Al Barsha',
            'Jumeirah',
            'Al Quoz',
            'Jumeirah Lakes Towers (JLT)',
            'Dubai Silicon Oasis',
            'Discovery Gardens',
            'Motor City',
            'Dubai Investment Park',
            'Arabian Ranches',
            'Emirates Hills',
            'The Greens',
            'The Views',
            'Dubai Sports City',
        ];

        foreach ($areas as $title) {
            Area::firstOrCreate([
                'name' => $title,
                'slug' => Str::slug($title),
            ]);
        }
    }
}
