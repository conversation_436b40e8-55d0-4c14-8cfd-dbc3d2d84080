<?php

namespace Database\Seeders;

use App\Models\Admin;
use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::query()
            ->updateOrCreate(
                [
                    'email' => '<EMAIL>',
                ],
                [
                    'name' => 'Khaled Waleed',
                    'email' => '<EMAIL>',
                    'password' => \Hash::make('123123123'),
                ]
            );

        User::factory()->count(10)->create();

        Admin::query()
            ->updateOrCreate([
                'email' => '<EMAIL>',
            ], [
                'name' => 'Khaled Waleed (Admin)',
                'email' => '<EMAIL>',
                'password' => \Hash::make('123123123'),
            ]);

        $this->call([
            TagSeeder::class,
            AreaSeeder::class,
            RetailDestinationSeeder::class,
            // PartnerSeeder::class,
            CreatorSeeder::class,
        ]);
    }
}
