<?php

namespace Database\Factories;

use App\Models\Collection;
use App\Models\CollectionItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class CollectionItemFactory extends Factory
{
    protected $model = CollectionItem::class;

    public function definition(): array
    {
        return [
            'collection_id' => Collection::factory(),
            'collectable_type' => null, // to be set in test
            'collectable_id' => null,   // to be set in test
        ];
    }
}
