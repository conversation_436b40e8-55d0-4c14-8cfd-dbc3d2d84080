<?php

namespace Database\Factories;

use App\Models\Deal;
use App\Models\DealSlot;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class DealSlotFactory extends Factory
{
    protected $model = DealSlot::class;

    public function definition(): array
    {
        return [
            'day' => Arr::random(array_keys(Carbon::getDays())),
            'from' => Carbon::now()->toTimeString(),
            'to' => Carbon::now()->addHours(2)->toTimeString(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'deal_id' => Deal::factory(),
        ];
    }
}
