<?php

namespace Database\Factories;

use App\Models\Partner;
use App\Models\Reel;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class ReelFactory extends Factory
{
    use WithoutModelEvents;

    protected $model = Reel::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->word(),
            'caption' => $this->faker->word(),
            'url' => $this->faker->imageUrl(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'partner_id' => Partner::factory(),
        ];
    }
}
