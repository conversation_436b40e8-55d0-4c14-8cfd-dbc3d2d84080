<?php

namespace Database\Factories;

use App\Enums\DealType;
use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Tag;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class DealFactory extends Factory
{
    protected $model = Deal::class;

    public function definition(): array
    {

        return [
            'title' => $this->faker->word(),
            'description' => $this->faker->text(),
            'max_saving' => $this->faker->randomFloat(1, 0, 100),
            'max_usage_per_day' => $this->faker->randomNumber(1),
            'reuse_limit_days' => $this->faker->randomNumber(2),
            'valid_days' => \Arr::random(array_keys(Carbon::getDays()), 2),
            'deal_type' => \Arr::random(DealType::cases()),
            'service_type_id' => Tag::inRandomOrder()->where('type', 'service_options')->first()?->id,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'partner_location_id' => PartnerLocation::factory(),
        ];
    }
}
