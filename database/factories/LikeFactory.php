<?php

namespace Database\Factories;

use App\Models\Like;
use Illuminate\Database\Eloquent\Factories\Factory;

class LikeFactory extends Factory
{
    protected $model = Like::class;

    public function definition(): array
    {
        return [
            'user_id' => \App\Models\User::factory(),
            'likeable_id' => \App\Models\PartnerLocation::factory(),
            'likeable_type' => (new \App\Models\PartnerLocation)->getMorphClass(),
        ];
    }
}
