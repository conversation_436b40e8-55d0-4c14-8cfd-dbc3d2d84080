<?php

namespace Database\Factories;

use App\Models\Area;
use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\RetailDestination;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class PartnerLocationFactory extends Factory
{
    protected $model = PartnerLocation::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),

            'google_rate' => $this->faker->randomFloat(min: 0, max: 5, nbMaxDecimals: 1),
            'reviews_count' => $this->faker->randomNumber(2),

            'price_per_person' => $this->faker->randomFloat(min: 0, max: 100),

            'opening_hours' => [
                [
                    'day' => \Arr::random(array_keys(Carbon::getDays())),
                    'from' => $this->faker->time('H:i'),
                    'to' => $this->faker->time('H:i'),
                ],
            ],

            'phone' => $this->faker->phoneNumber(),
            'address_line_1' => $this->faker->address(),
            'address_line_2' => $this->faker->address(),
            'city' => $this->faker->city(),
            'state' => $this->faker->word(),
            'postal_code' => $this->faker->postcode(),
            'country' => $this->faker->country(),

            'lat' => $this->faker->latitude(),
            'lng' => $this->faker->longitude(),

            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'partner_id' => Partner::factory(),
            'area_id' => Area::factory(),
            'retail_destination_id' => RetailDestination::factory(),
        ];
    }

    public function openingHours(?int $day = null, ?string $from = null, ?string $to = null): static
    {
        return $this->state(fn (array $attributes) => [
            'opening_hours' => [
                [
                    'day' => $day ?? \Arr::random(array_keys(Carbon::getDays())),
                    'from' => $from ?? $this->faker->time('H:i'),
                    'to' => $to ?? $this->faker->time('H:i'),
                ],
            ],
        ]);
    }
}
