id: 69418
name: conari-dev
environments:
  staging:
    runtime: docker-arm
    dockerfile: Dockerfile
    domain: conari.co
    scheduler: false
    storage: conari-dev-staging
    database: conari-dev-staging
    octane: true

    memory: 1024
    timeout: 60
    concurrency: 10

    cli-memory: 512
    cli-timeout: 120
    cli-concurrency: 3

    queue-memory: 512
    queue-timeout: 240
    queue-concurrency: 5

    build:
      - "COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev --optimize-autoloader"
      - "php artisan optimize"
      - "php artisan lighthouse:cache"
    deploy:
      - "php artisan migrate --force"
      - "php artisan lighthouse:validate-schema"
