<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum DealType: string implements <PERSON><PERSON>abe<PERSON>
{
    case FREE_ITEM_WITH_PURCHASE = 'free_item_with_purchase';
    case TWO_FOR_ONE = '2for1';
    case AED_40_DISCOUNT = 'aed_40_discount';
    case ONE_AED_SPECIAL = '1_aed_special';

    public function label(): string
    {
        return match ($this) {
            self::FREE_ITEM_WITH_PURCHASE => 'Free Item with a Purchase',
            self::TWO_FOR_ONE => '2for1',
            self::AED_40_DISCOUNT => 'AED 40 Discount',
            self::ONE_AED_SPECIAL => '1 AED Special',
        };
    }

    public function getLabel(): string
    {
        return $this->label();
    }
}
