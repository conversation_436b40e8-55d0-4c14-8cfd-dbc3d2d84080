<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum InvitationStatus: string implements Has<PERSON>abel
{
    case PENDING = 'pending';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';

    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::APPROVED => 'Approved',
            self::REJECTED => 'Rejected',
        };
    }

    public function getLabel(): string
    {
        return $this->label();
    }
}
