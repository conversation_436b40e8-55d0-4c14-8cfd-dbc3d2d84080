<?php

namespace App\Models;

use App\Contracts\CanBeAddedToCollection;
use App\Contracts\CanUploadReel;
use App\Contracts\Likeable;
use App\Jobs\ExtractThumbnailFromReelJob;
use App\Traits\HasCollectionItems;
use App\Traits\HasLikes;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
// Added import
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Auth;
use Laravel\Scout\Searchable;
use Livewire\Component;
use Storage; // Added import for Livewire Component

/** @property string $thumbnail */
class Reel extends Model implements CanBeAddedToCollection, Likeable
{
    use HasCollectionItems, HasFactory, HasLikes, Searchable;

    protected $guarded = [];

    protected static function booted(): void
    {
        static::deleting(function (Reel $reel) {
            $reel->locations()->detach();
            CollectionItem::where('collectable_type', "App\Models\Reel")->where('collectable_id', $reel->id)->delete();
        });
    }

    public function partner(): BelongsTo
    {
        return $this->belongsTo(Partner::class);
    }

    public function locations(): BelongsToMany
    {
        return $this->belongsToMany(PartnerLocation::class, 'reel_location');
    }

    public function toSearchableArray(): array
    {
        /** @var Partner $partner */
        $partner = $this->partner;

        /** @var \Illuminate\Database\Eloquent\Collection<int, PartnerLocation>|null $locations */
        $locations = $this->locations;

        /** @var CanUploadReel $uploader */
        $uploader = $this->creator;

        $tags = ['by_place'];

        if ($uploader instanceof Creator) {
            $tags = ['by_creator'];
        }

        return [
            'objectID' => $this->id,
            'caption' => $this->caption,
            '_geoloc' => $locations?->pluck('location')->toArray(),
            'url' => Storage::disk(config('filesystems.default'))->url($this->url),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'partner' => $partner->toSearchableArray(),
            'places' => $locations?->map(function ($location) { // Added null safe operator
                /** @var PartnerLocation $location */
                return $location->toSearchableArray();
            }),
            '_tags' => $tags,
            ...$uploader instanceof Creator ? [
                'creator' => $uploader->toSearchableArray(),
            ] : [],
        ];
    }

    public function toArray()
    {
        return $this->toSearchableArray();
    }

    public function shouldBeSearchable(): bool
    {
        // @phpstan-ignore-next-line
        return $this->locations->filter(fn (PartnerLocation $partnerLocation) => $partnerLocation->deals()->count() >= 2)->count() >= 1;
    }

    public function isLiked(): Attribute
    {
        return Attribute::make(
            get: fn ($value, array $attributes) => auth()->check() ? $this->likes()->where('user_id', auth()->id())->exists() : false,
        );
    }

    public function isPlaceFollowed(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (! auth()->check()) {
                    return false;
                }

                /** @var ?PartnerLocation $firstLocation */
                $firstLocation = $this->locations->first();

                if (! $firstLocation) {
                    return false;
                }

                /** @var User $user */
                $user = auth()->user();

                return $user->following_places()->where('place_id', $firstLocation->id)->exists();
            },
        );
    }

    public function isCreatorFollowed(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (! auth()->check()) {
                    return false;
                }

                /** @var ?Creator $creator */
                $creator = $this->creator;

                if (! $creator) {
                    return false;
                }

                /** @var User $user */
                $user = auth()->user();

                return $user->following_creators()->where('creator_id', $creator->id)->exists();
            },
        );
    }

    protected function isPlaceInCollection(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (! Auth::check()) {
                    return false;
                }
                /** @var \App\Models\User $user */
                $user = Auth::user();

                $reelLocations = $this->locations()->pluck('id')->toArray();
                if (empty($reelLocations)) {
                    return false;
                }

                return $user->ownedCollections()
                    ->whereHas('items', function ($query) use ($reelLocations) {
                        $query->where('collectable_type', PartnerLocation::class)
                            ->where('collectable_id', $reelLocations[0]);
                    })
                    ->exists();
            }
        );
    }

    protected function savedPlacesCount(): Attribute
    {
        return Attribute::make(
            get: function () {
                $reelLocations = $this->locations()->pluck('id')->toArray();
                if (empty($reelLocations)) {
                    return 0;
                }

                return CollectionItem::where('collectable_type', PartnerLocation::class)
                    ->where('collectable_id', $reelLocations[0])
                    ->count();
            }
        );
    }

    public function scopeByIds(Builder $query, $args)
    {
        $query->whereIn('id', $args['ids']);
    }

    public static function getUploadReelsAction(): Action
    {
        return Action::make('upload_reels')
            ->form([
                Repeater::make('reels')
                    ->columns()
                    ->schema([
                        FileUpload::make('url')
                            ->panelAspectRatio('9:16')
                            ->label('Video')
                            ->required(),

                        TextInput::make('caption')
                            ->label('Caption'),

                        FileUpload::make('thumbnail'),
                    ]),
            ])
            ->action(function (array $data, Component $livewire) { // Changed type hint to Livewire\Component

                /** @var PartnerLocation $partnerPlace */
                $partnerPlace = $livewire->getOwnerRecord(); // @phpstan-ignore-line

                foreach ($data['reels'] as $reelData) {
                    $reel = Reel::query()->create([
                        'caption' => $reelData['caption'] ?? null,
                        'url' => $reelData['url'],
                        'partner_id' => $partnerPlace->partner->id ?? null,
                        'thumbnail' => $reelData['thumbnail'] ?? null,
                    ]);

                    $reel->locations()->attach($partnerPlace->id);

                    if (! $reelData['thumbnail']) {
                        ExtractThumbnailFromReelJob::dispatch($reel);
                    } else {
                        /** @var Partner $partner */
                        $partner = $partnerPlace->partner;

                        \Illuminate\Support\Facades\Storage::move($reelData['thumbnail'], "reels/{$partner->id}/{$reel->id}/thumbnail.jpg");

                        $reel->update([
                            'thumbnail' => "reels/{$partner->id}/{$reel->id}/thumbnail.jpg",
                        ]);
                    }
                }
            });

    }

    public function scopeFilterByCreator($query, $value)
    {
        if ($value) {
            return $query->where('creatable_type', Creator::class);
        }

        return $query;
    }

    public function creator(): MorphTo
    {
        return $this->morphTo('creatable');
    }

    protected function fullUrl(): Attribute
    {
        return Attribute::make(
            get: fn ($value, array $attributes) => Storage::url($this->url),
        );
    }

    protected function thumbnail(): Attribute
    {
        return Attribute::make(
            get: fn ($value, array $attributes) => $attributes['thumbnail'] ? Storage::url($attributes['thumbnail']) : null,
            set: fn ($value, array $attributes) => $value,
        );
    }
}
