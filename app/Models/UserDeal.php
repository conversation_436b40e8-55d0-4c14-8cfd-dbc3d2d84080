<?php

namespace App\Models;

use App\StateManagers\Deal\DealStateManager;
use App\StateManagers\Deal\NoShowDealState;
use App\StateManagers\Deal\RedeemableDealState;
use App\StateManagers\Deal\RedeemedDealState;
use App\StateManagers\Deal\UpcomingDealState;
use Database\Factories\UserDealFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * @property string $status One of: upcoming, redeemable, redeemed, noshow
 */
class UserDeal extends Pivot
{
    /** @use HasFactory<UserDealFactory> */
    use HasFactory;

    protected static function booted(): void
    {
        // static::updated(function (UserDeal $userDeal) {
        //     $userDeal->clearLightHouseCaches();
        // });
    }

    protected $guarded = ['id'];

    protected $table = 'user_deal';

    public $incrementing = true;

    protected $casts = [
        'reserve_slot' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function deal(): BelongsTo
    {
        return $this->belongsTo(Deal::class);
    }

    protected function casts(): array
    {
        return [
            'reserved_at' => 'datetime',
            'redeemed_at' => 'datetime',
        ];
    }

    public function state(): DealStateManager
    {
        return match ($this->status) {
            'upcoming' => new UpcomingDealState($this),
            'redeemable' => new RedeemableDealState($this),
            'redeemed' => new RedeemedDealState($this),
            'no-show' => new NoShowDealState($this),
            default => throw new \Exception('Invalid Status'),
        };
    }

    public function getCacheKeys(): array
    {
        return [
            'myDeal:id',
        ];
    }
}
