<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Partner extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $touches = ['locations'];

    protected static function booted(): void
    {
        static::deleting(function (Partner $partner) {
            $partner->locations()->get()->each(function ($location) {
                $location->delete();
            });
        });
    }

    public function toSearchableArray(): array
    {
        return [
            'id' => (string) $this->id,
            'name' => $this->name,
        ];
    }

    public function reels(): HasMany
    {
        return $this->hasMany(Reel::class, 'partner_id');
    }

    public function locations(): HasMany
    {
        return $this->hasMany(PartnerLocation::class, 'partner_id');
    }
}
