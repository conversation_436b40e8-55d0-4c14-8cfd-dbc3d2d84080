<?php

namespace App\Models;

use App\Enums\InvitationStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property string $email
 * @property string $name
 * @property string $provider
 * @property string $provider_id
 * @property InvitationStatus $status
 * @property Carbon $expires_at
 * @property-read bool $is_expired
 */
class Invitation extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'name',
        'provider',
        'provider_id',
        'status',
        'expires_at',
    ];

    protected $casts = [
        'status' => InvitationStatus::class,
        'expires_at' => 'datetime',
    ];

    public function isExpired(): bool
    {
        return now()->gt($this->expires_at);
    }

    public function isPending(): bool
    {
        return $this->status === InvitationStatus::PENDING;
    }

    public function isApproved(): bool
    {
        return $this->status === InvitationStatus::APPROVED;
    }
}
