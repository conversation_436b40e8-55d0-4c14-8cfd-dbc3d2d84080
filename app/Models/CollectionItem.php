<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes; // Add this line

class CollectionItem extends Model
{
    use HasFactory, SoftDeletes; // Add SoftDeletes here

    protected $guarded = ['id'];

    public function collection(): BelongsTo
    {
        return $this->belongsTo(Collection::class);
    }

    public function collectable(): MorphTo
    {
        return $this->morphTo();
    }
}
