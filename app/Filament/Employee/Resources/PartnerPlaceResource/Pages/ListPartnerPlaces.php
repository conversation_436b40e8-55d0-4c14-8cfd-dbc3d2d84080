<?php

namespace App\Filament\Employee\Resources\PartnerPlaceResource\Pages;

use App\Filament\Employee\Resources\PartnerPlaceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPartnerPlaces extends ListRecords
{
    protected static string $resource = PartnerPlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
