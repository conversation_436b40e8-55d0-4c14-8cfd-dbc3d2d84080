<?php

namespace App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers;

use App\Models\UserDeal;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class UserDealsRelationManager extends RelationManager
{
    protected static string $relationship = 'user_deals';

    protected static ?string $title = 'User Deals';

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('deal.title')
                    ->label('Deal')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('reserve_slot.date')
                    ->label('Date & Time')
                    ->description(function (UserDeal $record) {
                        $state = $record->reserve_slot;

                        if (! $state) {
                            return '';
                        }

                        $from = $state['slot']['from'];
                        $to = $state['slot']['to'];

                        return "{$from} - {$to}";
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('reuse_after'),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'used' => 'Used',
                        'expired' => 'Expired',
                        'cancelled' => 'Cancelled',
                    ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
