<?php

namespace App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers;

use App\Filament\Resources\DealResource;
use App\Models\Deal;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Table;

class DealsRelationManager extends RelationManager
{
    protected static string $relationship = 'deals';

    public function form(Form $form): Form
    {
        return DealResource::form($form)->columns(1);
    }

    public function table(Table $table): Table
    {
        return DealResource::table($table)
            ->actions([
                EditAction::make()->url(fn (Deal $record): string => \App\Filament\Employee\Resources\DealResource::getUrl('edit', ['record' => $record->id])),
                DeleteAction::make(),
            ]);
    }
}
