<?php

namespace App\Filament\Employee\Resources;

use App\Filament\Employee\Resources\PartnerPlaceResource\Pages;
use App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers\DealsRelationManager;
use App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers\ReelsRelationManager;
use App\Filament\Employee\Resources\PartnerPlaceResource\RelationManagers\UserDealsRelationManager;
use App\Models\PartnerLocation;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class PartnerPlaceResource extends Resource
{
    protected static ?string $model = PartnerLocation::class;

    protected static ?string $navigationIcon = 'heroicon-o-bookmark-square';

    protected static ?string $navigationLabel = 'Partner Places';

    public static function form(Form $form): Form
    {
        return \App\Filament\Resources\PartnerPlaceResource::form($form);
    }

    public static function table(Table $table): Table
    {
        return \App\Filament\Resources\PartnerPlaceResource::table($table);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('employees', fn (Builder $query) => $query->where('employee_id', auth()->id()));
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPartnerPlaces::route('/'),
            'create' => Pages\CreatePartnerPlace::route('/create'),
            'edit' => Pages\EditPartnerPlace::route('/{record}/edit'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            DealsRelationManager::class,
            ReelsRelationManager::class,
            UserDealsRelationManager::class,
        ];
    }
}
