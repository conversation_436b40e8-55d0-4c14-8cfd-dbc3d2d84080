<?php

namespace App\Filament\Employee\Resources;

use App\Filament\Employee\Resources\DealResource\Pages;
use App\Models\Deal;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DealResource extends Resource
{
    protected static ?string $model = Deal::class;

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return \App\Filament\Resources\DealResource::form($form);
    }

    public static function table(Table $table): Table
    {
        return \App\Filament\Resources\DealResource::table($table)
            ->actions([
                EditAction::make()->url(fn (Deal $record): string => DealResource::getUrl('edit', ['record' => $record->id])),
                DeleteAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeals::route('/'),
            'create' => Pages\CreateDeal::route('/create'),
            'edit' => Pages\EditDeal::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('partner_place.employees', fn (Builder $query) => $query->where('employee_id', auth()->id()));
    }
}
