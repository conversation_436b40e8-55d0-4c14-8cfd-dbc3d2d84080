<?php

namespace App\Filament\Traits;

use Illuminate\Support\Collection;

trait InteractsWithTags
{
    public function getFormTags(array $data): Collection
    {
        return collect($data[\App\Enums\TagType::AMBIANCE->value] ?? [])
            ->merge($data[\App\Enums\TagType::MEAL_TIMES->value] ?? [])
            ->merge($data[\App\Enums\TagType::SPECIALITIES->value] ?? [])
            ->merge($data[\App\Enums\TagType::SERVICE_OPTIONS->value] ?? [])
            ->merge($data[\App\Enums\TagType::DIETARY->value] ?? [])
            ->merge($data[\App\Enums\TagType::PARKING->value] ?? [])
            ->merge($data[\App\Enums\TagType::CUISINE_TYPES->value] ?? [])
            ->merge($data[\App\Enums\TagType::CRAVINGS->value] ?? [])
            ->unique();
    }

    public function clearFormTags(array $data): array
    {
        unset(
            $data[\App\Enums\TagType::AMBIANCE->value],
            $data[\App\Enums\TagType::MEAL_TIMES->value],
            $data[\App\Enums\TagType::SPECIALITIES->value],
            $data[\App\Enums\TagType::SERVICE_OPTIONS->value],
            $data[\App\Enums\TagType::DIETARY->value],
            $data[\App\Enums\TagType::PARKING->value],
            $data[\App\Enums\TagType::CUISINE_TYPES->value],
            $data[\App\Enums\TagType::CRAVINGS->value],
        );

        return $data;
    }
}
