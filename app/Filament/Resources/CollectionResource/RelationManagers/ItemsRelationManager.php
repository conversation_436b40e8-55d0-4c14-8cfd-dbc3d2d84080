<?php

namespace App\Filament\Resources\CollectionResource\RelationManagers;

use App\Models\CollectionItem; // Added import
use App\Models\Creator;
use App\Models\PartnerLocation;
use App\Models\Reel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table; // Add this line

class ItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('collectable_type')
                    ->options([
                        PartnerLocation::class => 'Partner Location',
                        Creator::class => 'Creator',
                        Reel::class => 'Reel',
                    ])
                    ->required()
                    ->live()
                    ->afterStateUpdated(function (Set $set) {
                        $set('collectable_id', null);
                    }),
                Forms\Components\Select::make('collectable_id')
                    ->label('Item')
                    ->options(function (Get $get) {
                        $type = $get('collectable_type');
                        if (! $type) {
                            return [];
                        }
                        // Assuming 'name' for PartnerLocation and Creator, 'title' for Reel
                        // Adjust the display attribute as necessary
                        $displayAttribute = match ($type) {
                            Reel::class => 'title',
                            default => 'name',
                        };
                        $items = $type::query()->pluck($displayAttribute, 'id')->all();

                        // Ensure all labels are strings to prevent errors with null values
                        return array_map(function ($label) {
                            return $label ?? 'Untitled'; // Replace null labels with an empty string or a placeholder like '(Untitled)'
                        }, $items);
                    })
                    ->searchable()
                    ->preload()
                    ->required()
                    ->key(fn (Get $get) => $get('collectable_type')), // Re-render when type changes
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('collectable_id') // Or a more descriptive attribute if available directly on CollectionItem
            ->columns([

                Tables\Columns\TextColumn::make('collectable_type')
                    ->label('Type')
                    ->formatStateUsing(function (string $state): string {
                        return match ($state) {
                            PartnerLocation::class => 'Partner Location',
                            Creator::class => 'Creator',
                            Reel::class => 'Reel',
                            default => $state,
                        };
                    })
                    ->color(fn (CollectionItem $record) => $record->trashed() ? 'gray' : null), // Dim text if trashed
                Tables\Columns\TextColumn::make('collectable_id')
                    ->color(fn (CollectionItem $record) => $record->trashed() ? 'gray' : null), // Dim text if trashed
                Tables\Columns\TextColumn::make('collectable.name') // Example if all collectables had a 'name'
                    ->label('Collected Item Name')
                    ->getStateUsing(function (CollectionItem $record): string { // Changed type hint to CollectionItem
                        if ($record->collectable) {
                            return (string) ($record->collectable->name ?? $record->collectable->title ?? $record->collectable->getKey());
                        }

                        return 'N/A';
                    })
                    ->searchable(query: function (\Illuminate\Database\Eloquent\Builder $query, string $search): \Illuminate\Database\Eloquent\Builder {
                        return $query->where(function (\Illuminate\Database\Eloquent\Builder $subQuery) use ($search) {
                            $searchPattern = "%{$search}%";
                            $subQuery->orWhereHasMorph(
                                'collectable',
                                [PartnerLocation::class, Creator::class],
                                function (\Illuminate\Database\Eloquent\Builder $q) use ($searchPattern) {
                                    $q->where('name', 'LIKE', $searchPattern)
                                        ->orWhere('id', 'LIKE', $searchPattern);
                                }
                            )
                                ->orWhereHasMorph(
                                    'collectable',
                                    [Reel::class],
                                    function (\Illuminate\Database\Eloquent\Builder $q) use ($searchPattern) {
                                        $q->where('title', 'LIKE', $searchPattern)
                                            ->orWhere('id', 'LIKE', $searchPattern);
                                    }
                                );
                        });
                    })
                    ->color(fn (CollectionItem $record) => $record->trashed() ? 'gray' : null), // Dim text if trashed
                Tables\Columns\TextColumn::make('created_at')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime(),
                Tables\Columns\TextColumn::make('deleted_at') // Add deleted_at column
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(), // Add TrashedFilter
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->hidden(fn (CollectionItem $record): bool => $record->trashed()), // Hide if trashed
                Tables\Actions\DeleteAction::make()
                    ->hidden(fn (CollectionItem $record): bool => $record->trashed()), // Hide if trashed
                Tables\Actions\ForceDeleteAction::make()
                    ->visible(fn (CollectionItem $record): bool => $record->trashed()), // Show only if trashed
                Tables\Actions\RestoreAction::make()
                    ->visible(fn (CollectionItem $record): bool => $record->trashed()),   // Show only if trashed
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(), // Add ForceDeleteBulkAction
                    Tables\Actions\RestoreBulkAction::make(),   // Add RestoreBulkAction
                ]),
            ]);
    }
}
