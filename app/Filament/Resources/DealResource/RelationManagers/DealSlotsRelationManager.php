<?php

namespace App\Filament\Resources\DealResource\RelationManagers;

use App\Models\DealSlot;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class DealSlotsRelationManager extends RelationManager
{
    protected static string $relationship = 'dealSlots';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('day')
                    ->required()
                    ->integer(),

                DatePicker::make('from'),

                DatePicker::make('to'),

                Placeholder::make('created_at')
                    ->label('Created Date')
                    ->content(fn (?DealSlot $record): string => $record?->created_at?->diffForHumans() ?? '-'),

                Placeholder::make('updated_at')
                    ->label('Last Modified Date')
                    ->content(fn (?DealSlot $record): string => $record?->updated_at?->diffForHumans() ?? '-'),

                TextInput::make('available_seats')
                    ->required()
                    ->integer(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                TextColumn::make('day'),

                TextColumn::make('from')
                    ->date(),

                TextColumn::make('to')
                    ->date(),

                TextColumn::make('available_seats'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make(),
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
