<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CollectionResource\Pages;
use App\Filament\Resources\CollectionResource\RelationManagers\ItemsRelationManager;
use App\Models\Collection;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;

class CollectionResource extends Resource
{
    protected static ?string $model = Collection::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Collections';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name') // Assuming User model has a 'name' attribute
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->columnSpanFull(),
            ])->disabled(fn (?Collection $record): bool => $record?->trashed() ?? false); // Disable form if record is trashed

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->color(fn (?Collection $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('user.name') // Assuming User model has a 'name' attribute
                    ->searchable()
                    ->sortable()
                    ->color(fn (?Collection $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('items_count')->counts('items')
                    ->label('Items Count')
                    ->sortable()
                    ->color(fn (?Collection $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),
                TrashedFilter::make(),
            ])
            ->actions([
                EditAction::make()->hidden(fn (?Collection $record) => $record?->trashed() ?? false),
                DeleteAction::make()->hidden(fn (?Collection $record) => $record?->trashed() ?? false),
                ForceDeleteAction::make()->visible(fn (?Collection $record) => $record?->trashed() ?? false),
                RestoreAction::make()->visible(fn (?Collection $record) => $record?->trashed() ?? false),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->recordUrl(fn (?Collection $record): ?string => ($record?->trashed() || ! Pages\EditCollection::canAccess(['record' => $record])) ? null : Pages\EditCollection::getUrl(['record' => $record]));
    }

    public static function getRelations(): array
    {
        return [
            ItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCollections::route('/'),
            'create' => Pages\CreateCollection::route('/create'),
            'edit' => Pages\EditCollection::route('/{record}/edit'),
        ];
    }
}
