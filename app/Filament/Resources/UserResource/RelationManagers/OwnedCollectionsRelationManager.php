<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Models\Collection; // Added import
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\DeleteAction; // Added import
use Filament\Tables\Actions\EditAction; // Added import
use Filament\Tables\Actions\ForceDeleteAction; // Added import
use Filament\Tables\Actions\RestoreAction; // Added import
use Filament\Tables\Filters\TrashedFilter; // Added import
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model; // Keep Model for general type hint if needed, but prefer specific
use Illuminate\Database\Eloquent\SoftDeletingScope;

class OwnedCollectionsRelationManager extends RelationManager
{
    protected static string $relationship = 'ownedCollections';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->columnSpanFull(),
            ])->disabled(fn (?Collection $record): bool => $record?->trashed() ?? false); // Use Collection type hint
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->color(fn (?Collection $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('items_count')->counts('items')
                    ->label('Items Count')
                    ->sortable()
                    ->color(fn (?Collection $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->color(fn (?Collection $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->color(fn (?Collection $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->color(fn (?Collection $record) => $record?->trashed() ? 'gray' : null),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                EditAction::make()->hidden(fn (?Collection $record) => $record?->trashed() ?? false),
                DeleteAction::make()->hidden(fn (?Collection $record) => $record?->trashed() ?? false),
                ForceDeleteAction::make()->visible(fn (?Collection $record) => $record?->trashed() ?? false),
                RestoreAction::make()->visible(fn (?Collection $record) => $record?->trashed() ?? false),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]));
    }

    // It's good practice to define getEloquentQuery if you are modifying query logic,
    // however, modifyQueryUsing is often preferred for relation managers.
    // If more complex query modifications are needed, uncomment and use this.
    // public static function getEloquentQuery(): Builder
    // {
    //     return parent::getEloquentQuery()->withoutGlobalScopes([
    //         SoftDeletingScope::class,
    //     ]);
    // }

    // Ensure the record URL logic is appropriate for a relation manager context.
    // Relation managers typically don't have their own "pages" like resources do.
    // The actions (Edit, View) handle navigation or modals.
    // If you need to prevent clicking on a row to navigate for trashed items,
    // this might be handled differently or might not be applicable if rows aren't inherently clickable to a separate page.
}
