<?php

namespace App\Filament\Resources\PartnerResource\RelationManagers;

use App\Filament\Resources\PartnerPlaceResource;
use App\Models\PartnerLocation;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class LocationsRelationManager extends RelationManager
{
    protected static string $relationship = 'locations';

    protected static ?string $title = 'Places';

    protected static ?string $modelLabel = 'Partner Place';

    public function form(Form $form): Form
    {
        return PartnerPlaceResource::form($form);
    }

    public function table(Table $table): Table
    {
        return PartnerPlaceResource::table($table)
            ->recordTitleAttribute('name')
            ->recordUrl(fn (PartnerLocation $partnerLocation) => PartnerPlaceResource::getUrl('edit', ['record' => $partnerLocation->id]))
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->after(function (PartnerLocation $partnerLocation, array $data) {
                        $tags = collect($data['diningModes'] ?? [])
                            ->merge($data['categories'] ?? [])
                            ->merge($data[\App\Enums\TagType::SPECIALITIES->value] ?? [])
                            ->merge($data[\App\Enums\TagType::AMBIANCE->value] ?? [])
                            ->merge($data[\App\Enums\TagType::PARKING->value] ?? []);

                        $partnerLocation->tags()->attach(
                            $tags->unique()->all()
                        );
                    }),
            ]);
    }
}
