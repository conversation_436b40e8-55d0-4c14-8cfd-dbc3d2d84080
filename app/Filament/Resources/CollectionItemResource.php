<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CollectionItemResource\Pages;
use App\Models\CollectionItem;
use App\Models\Creator;
use App\Models\PartnerLocation;
use App\Models\Reel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ForceDeleteAction;
use Filament\Tables\Actions\RestoreAction;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CollectionItemResource extends Resource
{
    protected static ?string $model = CollectionItem::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Collections';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('collection_id')
                    ->relationship(name: 'collection', titleAttribute: 'title')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Select::make('collectable_type')
                    ->options([
                        PartnerLocation::class => 'Partner Location',
                        Creator::class => 'Creator',
                        Reel::class => 'Reel',
                    ])
                    ->required()
                    ->live()
                    ->afterStateUpdated(function (Set $set) {
                        $set('collectable_id', null);
                    }),
                Forms\Components\Select::make('collectable_id')
                    ->label('Item')
                    ->options(function (Get $get) {
                        $type = $get('collectable_type');
                        if (! $type) {
                            return [];
                        }
                        // Assuming 'name' for PartnerLocation and Creator, 'title' for Reel
                        // Adjust the display attribute as necessary
                        $displayAttribute = match ($type) {
                            Reel::class => 'title',
                            default => 'name',
                        };
                        $items = $type::query()->pluck($displayAttribute, 'id')->all();

                        // Ensure all labels are strings to prevent errors with null values
                        return array_map(function ($label) {
                            return $label ?? 'Untitled'; // Replace null labels with an empty string or a placeholder like '(Untitled)'
                        }, $items);
                    })
                    ->searchable()
                    ->preload()
                    ->required()
                    ->key(fn (Get $get) => $get('collectable_type')), // Re-render when type changes

            ])->disabled(fn (?CollectionItem $record): bool => $record?->trashed() ?? false); // Disable form if record is trashed
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('collection.title')
                    ->searchable()
                    ->sortable()
                    ->color(fn (?CollectionItem $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('collectable_type')
                    ->label('Type')
                    ->formatStateUsing(function (string $state): string {
                        return match ($state) {
                            PartnerLocation::class => 'Partner Location',
                            Creator::class => 'Creator',
                            Reel::class => 'Reel',
                            default => $state,
                        };
                    })
                    ->searchable()
                    ->color(fn (?CollectionItem $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('collectable_id')
                    ->numeric()
                    ->sortable()
                    ->color(fn (?CollectionItem $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('collectable.name')
                    ->label('Collecting Item Name')
                    ->getStateUsing(function (CollectionItem $record): string {
                        if ($record->collectable) {
                            $name = $record->collectable->name ?? $record->collectable->title ?? $record->collectable->getKey();

                            return (string) \Illuminate\Support\Str::of($name)
                                ->words(10, '...')
                                ->replaceMatches('/\s+/', ' ')
                                ->trim();
                        }

                        return 'N/A';
                    })
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHasMorph(
                            'collectable',
                            [PartnerLocation::class, Creator::class, Reel::class],
                            function (Builder $q, string $type) use ($search) {
                                if ($type === PartnerLocation::class || $type === Creator::class) {
                                    $q->where('name', 'like', "%{$search}%");
                                } elseif ($type === Reel::class) {
                                    $q->where('title', 'like', "%{$search}%");
                                }
                            }
                        );
                    })
                    ->color(fn (?CollectionItem $record) => $record?->trashed() ? 'gray' : null),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
            ])
            ->actions([
                EditAction::make()->hidden(fn (?CollectionItem $record) => $record?->trashed() ?? false),
                DeleteAction::make()->hidden(fn (?CollectionItem $record) => $record?->trashed() ?? false),
                ForceDeleteAction::make()->visible(fn (?CollectionItem $record) => $record?->trashed() ?? false),
                RestoreAction::make()->visible(fn (?CollectionItem $record) => $record?->trashed() ?? false),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])->recordUrl(fn (?CollectionItem $record): ?string => ($record?->trashed() || ! Pages\EditCollectionItem::canAccess(['record' => $record])) ? null : Pages\EditCollectionItem::getUrl(['record' => $record]));
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCollectionItems::route('/'),
            'create' => Pages\CreateCollectionItem::route('/create'),
            'edit' => Pages\EditCollectionItem::route('/{record}/edit'),
        ];
    }
}
