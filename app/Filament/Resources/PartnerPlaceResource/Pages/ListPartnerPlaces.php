<?php

namespace App\Filament\Resources\PartnerPlaceResource\Pages;

use App\Filament\Resources\PartnerPlaceResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListPartnerPlaces extends ListRecords
{
    protected static string $resource = PartnerPlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
