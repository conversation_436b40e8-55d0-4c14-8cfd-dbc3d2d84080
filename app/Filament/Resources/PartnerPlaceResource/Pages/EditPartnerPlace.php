<?php

namespace App\Filament\Resources\PartnerPlaceResource\Pages;

use App\Filament\Resources\PartnerPlaceResource;
use App\Filament\Traits\InteractsWithTags;
use App\Models\PartnerLocation;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditPartnerPlace extends EditRecord
{
    use InteractsWithTags;

    protected static string $resource = PartnerPlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }

    protected function afterValidate(): void
    {
        $tags = $this->getFormTags($this->data);

        if ($tags->unique()->count() < 3) {
            Notification::make()
                ->warning()
                ->title('Please Choose at least 3 tags.')
                ->persistent()
                ->send();

            $this->halt();
        }
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data = $this->clearFormTags($data);

        return $data;
    }

    public function afterSave(): void
    {
        /** @var PartnerLocation $partnerPlace */
        $partnerPlace = $this->getRecord();

        $partnerPlace->tags()->sync(
            $this->getFormTags($this->data)
        );
    }
}
