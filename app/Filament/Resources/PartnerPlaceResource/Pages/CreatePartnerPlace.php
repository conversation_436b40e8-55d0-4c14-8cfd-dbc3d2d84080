<?php

namespace App\Filament\Resources\PartnerPlaceResource\Pages;

use App\Filament\Resources\PartnerPlaceResource;
use App\Filament\Traits\InteractsWithTags;
use App\Models\PartnerLocation;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Validation\ValidationException;

class CreatePartnerPlace extends CreateRecord
{
    use InteractsWithTags;

    protected static string $resource = PartnerPlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [

        ];
    }

    protected function afterValidate(): void
    {
        $tags = $this->getFormTags($this->data);

        if ($tags->count() < 3) {
            Notification::make()
                ->warning()
                ->title('Please Choose at least 3 tags.')
                ->persistent()
                ->send();

            throw ValidationException::withMessages(['tags' => 'Please Choose at least 3 tags.']);
        }
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['admin_id'] = auth()->id();

        $data = $this->clearFormTags($data);

        return $data;
    }

    public function afterCreate(): void
    {
        /** @var PartnerLocation $partnerPlace */
        $partnerPlace = $this->getRecord();

        $partnerPlace->tags()->sync(
            $this->getFormTags($this->data)
        );
    }
}
