<?php

namespace App\Filament\Resources\ReelResource\RelationManagers;

use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\Reel;
use Filament\Forms\Components\Select;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class LocationsRelationManager extends RelationManager
{
    protected static string $relationship = 'locations';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->default(fn (PartnerLocation $record) => PartnerLocation::find($record->id)->name),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->form([
                        Select::make('recordId')
                            ->label('Location')

                            ->options(function () {
                                /** @var Reel $reel */
                                $reel = $this->getOwnerRecord();

                                /** @var Partner $partner */
                                $partner = $reel->partner;

                                if ($partner->exists) {
                                    return $partner->locations()
                                        ->whereNotIn('id', $reel->locations->pluck('id'))
                                        ->pluck('name', 'id');
                                }

                                return [];
                            }),
                    ])
                    ->action(function (array $data) {
                        /** @var Reel $reel */
                        $reel = $this->getOwnerRecord();

                        $reel->locations()->attach($data['recordId']);
                    }),
            ])
            ->actions([
                Tables\Actions\DetachAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make(),
                ]),
            ]);
    }
}
