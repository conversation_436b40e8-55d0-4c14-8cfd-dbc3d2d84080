<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Collection;

use App\Models\Collection;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class EditCollectionMutation
{
    /**
     * @param  mixed  $_  Always null, since this is a root field.
     * @param  array{input: array{id: string, title?: string, description?: string}}  $args  The arguments that were passed into the field.
     * @param  GraphQLContext  $context  The GraphQL context.
     * @param  ResolveInfo  $resolveInfo  Information about the query itself, such as the execution state, the field name, path to the field from the root, and more.
     *
     * @throws AuthorizationException
     */
    public function __invoke(mixed $_, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): Collection
    {
        $input = $args['input'];
        $collection = Collection::findOrFail($input['id']);

        if ($collection->user_id !== Auth::id()) {
            throw new AuthorizationException('You are not authorized to edit this collection.');
        }

        if (isset($input['title'])) {
            $collection->title = $input['title'];
        }

        if (array_key_exists('description', $input)) { // Check if description is explicitly passed, even if null
            $collection->description = $input['description'];
        }

        $collection->save();

        return $collection;
    }
}
