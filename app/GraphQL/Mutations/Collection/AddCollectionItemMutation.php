<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Collection;

use App\Actions\Collections\CreateCollection;
use App\Models\Collection;
use App\Models\User;
use GraphQL\Error\Error as GraphQLError;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class AddCollectionItemMutation
{
    /**
     * @param  mixed  $_
     * @param  array<string, mixed>  $args
     * @return array<Collection>
     *
     * @throws GraphQLError
     */
    public function __invoke($_, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        /** @var User $user */
        $user = $context->user();
        $input = $args['input'];
        $createCollectionAction = new CreateCollection;

        $collectableType = strtoupper($input['collectable_type'] ?? 'PARTNER_LOCATION'); // Default type
        $modelClass = self::resolveModelClass($collectableType);

        if (empty($modelClass)) {
            throw new GraphQLError("Invalid collectable_type provided: {$collectableType}. Supported types are REEL, CREATOR, PARTNER_LOCATION.");
        }

        // Check if the actual item exists
        $collectableModel = app($modelClass);
        if (! isset($input['collectable_id']) || ! $collectableModel->where('id', $input['collectable_id'])->exists()) {
            $collectableId = $input['collectable_id'] ?? 'null';
            throw new GraphQLError("Collectable item with ID {$collectableId} of type {$collectableType} not found.");
        }

        $collections = [];
        $collection_ids = $input['collection_ids'] ?? []; // Ensure collection_ids is defined here

        if (! empty($collection_ids)) {
            foreach ($collection_ids as $collection_id) {
                /** @var ?Collection $collection */
                $collection = $user->ownedCollections()->find($collection_id);
                if ($collection) {
                    $this->addItemToSpecificCollection($collection, $modelClass, $input['collectable_id']);
                    $collections[] = $collection->fresh();
                } else {
                    // If a specific ID is invalid, create a new collection for this item
                    $new_collection = $createCollectionAction->handle($user, [
                        'title' => 'My Collection', // Default title
                        'description' => null,
                    ]);
                    $this->addItemToSpecificCollection($new_collection, $modelClass, $input['collectable_id']);
                    $collections[] = $new_collection->fresh();
                }
            }
        } else {
            // No collection_ids provided, or array was empty.
            // Create a new collection if the user has no collections, otherwise use the first one.
            /** @var ?Collection $collection */
            $collection = $user->ownedCollections()->first();
            if (! $collection) {
                $collection = $createCollectionAction->handle($user, [
                    'title' => 'My Collection', // Default title
                    'description' => null,
                ]);
            }
            $this->addItemToSpecificCollection($collection, $modelClass, $input['collectable_id']);
            $collections[] = $collection->fresh();
        }

        return $collections;
    }

    private function addItemToSpecificCollection(Collection $collection, string $modelClass, $collectable_id): void
    {
        $collection->items()->firstOrCreate([
            'collection_id' => $collection->id,
            'collectable_type' => $modelClass,
            'collectable_id' => $collectable_id,
        ]);
    }

    private static function resolveModelClass($collectableType): string
    {
        $enumToModel = [
            'REEL' => \App\Models\Reel::class,
            'CREATOR' => \App\Models\Creator::class,
            'PARTNER_LOCATION' => \App\Models\PartnerLocation::class,
        ];

        if (! array_key_exists($collectableType, $enumToModel)) {
            throw new \InvalidArgumentException("Unsupported collectable_type: {$collectableType}");
        }

        return $enumToModel[$collectableType];
    }
}
