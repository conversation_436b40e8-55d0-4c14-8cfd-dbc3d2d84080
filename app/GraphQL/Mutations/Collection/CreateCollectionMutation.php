<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Collection;

use App\Actions\Collections\CreateCollection;
use App\Models\User;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class CreateCollectionMutation
{
    public function __invoke($_, array $args, GraphQLContext $context)
    {
        /** @var User $user */
        $user = $context->user();
        $input = $args['input'];

        $createCollectionAction = new CreateCollection;
        $collection = $createCollectionAction->handle($user, $input);

        return $collection;
    }
}
