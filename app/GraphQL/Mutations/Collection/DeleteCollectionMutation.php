<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Collection;

use App\Models\Collection;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Auth\Access\AuthorizationException;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class DeleteCollectionMutation
{
    /**
     * @param  mixed  $_  Always null, since this is a root field.
     * @param  array{id: string}  $args  The arguments that were passed into the field.
     * @param  GraphQLContext  $context  The GraphQL context.
     * @param  ResolveInfo  $resolveInfo  Information about the query itself, such as the execution state, the field name, path to the field from the root, and more.
     *
     * @throws AuthorizationException
     */
    public function __invoke(mixed $_, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): bool
    {
        $collection = Collection::findOrFail($args['id']);

        // Check if the authenticated user owns the collection
        if ($collection->user_id !== $context->user()->getKey()) {
            throw new AuthorizationException('You are not authorized to delete this collection.');
        }

        return (bool) $collection->delete();
    }
}
