<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Collection;

use App\Models\User;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class RemoveItemFromCollectionMutation
{
    /**
     * @param  array{input: array{collection_ids: array<string>, collectable_id: string, collectable_type: string}}  $args
     *
     * @throws AuthorizationException
     */
    public function __invoke(mixed $_, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): bool
    {
        /** @var User $user */
        $user = Auth::user();
        $input = $args['input'];

        $collection_ids = $input['collection_ids'];
        $collectable_id = $input['collectable_id'];
        $collectable_type_string = $input['collectable_type'];

        $modelClass = $this->resolveModelClass($collectable_type_string);

        if (! $modelClass) {
            throw new AuthorizationException('Invalid collectable type provided.');
        }

        $deleted_count = 0;

        DB::transaction(function () use ($user, $collection_ids, $modelClass, $collectable_id, &$deleted_count) {
            foreach ($collection_ids as $collection_id) {
                /** @var \App\Models\Collection|null $collection */
                $collection = $user->ownedCollections()->find($collection_id);

                if ($collection) {
                    $items_deleted_in_this_collection = $collection->items()
                        ->where('collectable_id', $collectable_id)
                        ->where('collectable_type', $modelClass)
                        ->delete();

                    if ($items_deleted_in_this_collection > 0) {
                        $deleted_count++;
                    }
                }
            }
        });

        return $deleted_count > 0;
    }

    private function resolveModelClass(string $collectableType): ?string
    {
        $enumToModel = [
            'REEL' => \App\Models\Reel::class,
            'CREATOR' => \App\Models\Creator::class,
            'PARTNER_LOCATION' => \App\Models\PartnerLocation::class,
        ];

        return $enumToModel[$collectableType] ?? null;
    }
}
