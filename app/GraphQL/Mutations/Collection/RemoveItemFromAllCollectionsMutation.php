<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Collection;

use App\Models\CollectionItem;
use App\Models\User;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\DB;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class RemoveItemFromAllCollectionsMutation
{
    /**
     * @param  array{input: array{collectable_id: string, collectable_type: string}}  $args
     *
     * @throws AuthorizationException
     */
    public function __invoke(mixed $_, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): bool
    {
        /** @var User $user */
        $user = $context->user();
        $input = $args['input'];

        $collectable_id = $input['collectable_id'];
        $collectable_type_string = strtoupper($input['collectable_type']);
        $modelClass = $this->resolveModelClass($collectable_type_string);

        if (! $modelClass) {
            throw new AuthorizationException('Invalid collectable type provided.');
        }

        $deleted_count = 0;

        DB::transaction(function () use ($user, $modelClass, $collectable_id, &$deleted_count) {
            // Get all collection IDs owned by the user
            $collection_ids = $user->ownedCollections()->pluck('id')->toArray();

            if (empty($collection_ids)) {
                return; // No collections to remove from
            }

            // Delete the item from all collections owned by the user
            // This directly uses the CollectionItem model for a more efficient delete across multiple collections.
            $items_deleted = CollectionItem::query()
                ->whereIn('collection_id', $collection_ids)
                ->where('collectable_id', $collectable_id)
                ->where('collectable_type', $modelClass)
                ->delete();

            if ($items_deleted > 0) {
                $deleted_count = $items_deleted; // Count of all item instances removed across collections
            }
        });

        return $deleted_count > 0;
    }

    private function resolveModelClass(string $collectableType): ?string
    {
        $enumToModel = [
            'REEL' => \App\Models\Reel::class,
            'CREATOR' => \App\Models\Creator::class,
            'PARTNER_LOCATION' => \App\Models\PartnerLocation::class,
        ];

        return $enumToModel[$collectableType] ?? null;
    }
}
