<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Actions\User\UserFollowCreator;
use App\Models\User;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class FollowCreatorMutation
{
    public function __invoke(null $_, array $args, GraphQLContext $context)
    {
        /** @var User $user */
        $user = $context->user();

        return UserFollowCreator::handle($user, (int) $args['input']['id']);
    }
}
