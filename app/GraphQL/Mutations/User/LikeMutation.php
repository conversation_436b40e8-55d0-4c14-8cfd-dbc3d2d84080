<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\User;

use App\Actions\User\UserLike;
use App\Models\Creator;
use App\Models\PartnerLocation;
use App\Models\Reel;
use App\Models\User;
use GraphQL\Error\UserError;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class LikeMutation
{
    public function __invoke(null $_, array $args, GraphQLContext $context)
    {
        /** @var User $user */
        $user = $context->user();

        /** @var array $input */
        $input = $args['input'];

        /** @var class-string<\App\Models\PartnerLocation|\App\Models\Creator|Reel> $likeableType */
        $likeableType = match ($input['type']) {
            'PARTNER_PLACE' => \App\Models\PartnerLocation::class,
            'CREATOR' => \App\Models\Creator::class,
            'REEL' => \App\Models\Reel::class,
            default => throw new UserError('Invalid type provided')
        };

        /** @var PartnerLocation|Creator|null $likeable */
        $likeable = $likeableType::query()->find($input['id']);

        if (! $likeable) {
            throw new UserError('Invalid id provided');
        }

        return UserLike::handle($user, $likeable);
    }
}
