<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Deal;

use App\Actions\Deal\ReserveDeal;

final readonly class ReserveDealMutation
{
    /**
     * Handle the ReserveDealMutation.
     *
     * @param  null  $_  Unused parameter.
     * @param  array  $args  The arguments for the mutation, including:
     *                       - 'input': An array containing:
     *                       - 'id' (string): The ID of the deal.
     *                       - 'reserve_slot' (array): array containing:
     *                       -              'date' (string): The date for the reservation.
     *                       -              'slot' (array): An array containing:
     *                       -                  'from' (string): The start time of the slot.
     *                       -                  'to' (string): The end time of the slot.
     *                       - 'myDealIdToRenew' (string|null): The ID of the deal to renew, if applicable.
     * @return array An associative array containing:
     *               - 'myDeal': The result of the ReserveDeal::handle method, which processes the reservation.
     */
    public function __invoke(null $_, array $args)
    {
        return [
            'myDeal' => ReserveDeal::handle(dealId: $args['input']['id'], slot: $args['input']['reserve_slot'], myDealToRenew: $args['input']['myDealIdToRenew'] ?? null),
        ];
    }
}
