<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Invitation;

use App\Enums\InvitationStatus;
use App\Models\Invitation;
use App\Models\User;
use GraphQL\Error\Error;

final readonly class AcceptInvitationMutation
{
    /** @param  array{}  $args */
    public function __invoke(null $_, array $args): array
    {
        /** @var User $user */
        $user = auth()->user();
        $token = $args['token'];
        $inviterUserId = $args['inviterUserId'];

        // Find invitation by token and inviter
        $invitation = Invitation::where('token', $token)
            ->where('inviter_user_id', $inviterUserId)
            ->first();

        // Check if invitation exists
        if (! $invitation) {
            throw new Error('Invalid invitation token', null, null, [], null, null, [
                'statusCode' => 404,
                'message' => 'Invalid invitation token',
            ]);
        }

        // Check if invitation is already used
        if ($invitation->is_used) {
            throw new Error('Invitation has already been used', null, null, [], null, null, [
                'statusCode' => 401,
                'message' => 'Invitation has already been used',
            ]);
        }

        // Check if invitation token is expired
        if ($invitation->isTokenExpired()) {
            throw new Error('Invitation link has expired', null, null, [], null, null, [
                'statusCode' => 401,
                'message' => 'Invitation link has expired',
            ]);
        }

        // Check if inviter has exceeded their limit
        $inviter = User::find($inviterUserId);
        if (! $inviter) {
            throw new Error('Inviter not found', null, null, [], null, null, [
                'statusCode' => 404,
                'message' => 'Inviter not found',
            ]);
        }

        $invitationInfo = $inviter->getInvitationInfo();
        if ($invitationInfo['remaining'] <= 0) {
            throw new Error('Inviter has exceeded their invitation limit', null, null, [], null, null, [
                'statusCode' => 400,
                'message' => 'Inviter has exceeded their invitation limit',
            ]);
        }

        // Check if user already has a pending invitation
        $existingInvitation = Invitation::where('email', $user->email)
            ->where('status', InvitationStatus::PENDING)
            ->first();

        if ($existingInvitation && ! $existingInvitation->isUserInvitation()) {
            // If user has pending admin invitation, set the referral and approve it
            $existingInvitation->update([
                'inviter_user_id' => $inviterUserId,
                'status' => InvitationStatus::APPROVED,
            ]);

            // Mark the token invitation as used
            $invitation->markAsUsed($user);

            return [
                'user' => $user,
                'statusCode' => 200,
            ];
        }

        // Update invitation with user details and mark as used
        $invitation->update([
            'email' => $user->email,
            'name' => $user->name,
            'provider' => $user->provider ?? '',
            'provider_id' => $user->provider_id ?? '',
            'status' => InvitationStatus::APPROVED,
            'is_used' => true,
            'invitee_user_id' => $user->id,
        ]);

        return [
            'user' => $user,
            'statusCode' => 200,
        ];
    }
}
