<?php

declare(strict_types=1);

namespace App\GraphQL\Mutations\Invitation;

use App\Models\Invitation;
use App\Models\User;
use GraphQL\Error\Error;
use Illuminate\Support\Carbon;

final readonly class InviteUserMutation
{
    /** @param  array{}  $args */
    public function __invoke(null $_, array $args): string
    {
        /** @var User $user */
        $user = auth()->user();

        // Check if user has remaining invitations
        $invitationInfo = $user->getInvitationInfo();
        if ($invitationInfo['remaining'] <= 0) {
            throw new Error('You have reached your invitation limit', null, null, [], null, null, [
                'statusCode' => 400,
                'message' => 'You have reached your invitation limit',
            ]);
        }

        // Generate invitation token
        $token = Invitation::generateToken();
        $tokenExpiresAt = Carbon::now()->addDays(config('invitation.token_expiry_days', 7));

        // Create invitation record
        $invitation = Invitation::create([
            'inviter_user_id' => $user->id,
            'token' => $token,
            'token_expires_at' => $tokenExpiresAt,
            'status' => \App\Enums\InvitationStatus::PENDING,
            'expires_at' => Carbon::now()->addDays(config('invitation.expiry_days', 30)),
            'email' => '', // Will be filled when invitation is accepted
            'name' => '', // Will be filled when invitation is accepted
            'provider' => '', // Will be filled when invitation is accepted
            'provider_id' => '', // Will be filled when invitation is accepted
            'is_used' => false,
        ]);

        // Generate invitation link
        $baseUrl = config('app.url');
        $invitationLink = "{$baseUrl}/invite?token={$token}&inviter={$user->id}";

        return $invitationLink;
    }
}
