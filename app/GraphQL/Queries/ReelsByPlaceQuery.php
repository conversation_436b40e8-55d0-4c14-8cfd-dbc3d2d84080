<?php

namespace App\GraphQL\Queries;

use App\Models\Reel;
use Illuminate\Database\Eloquent\Builder;

class ReelsByPlaceQuery
{
    /**
     * Resolve the query to get reels by partner location ID
     * using the many-to-many relationship.
     *
     * @param  mixed  $root
     */
    public function resolve($root, array $args): Builder
    {
        return Reel::query()
            ->whereHas('locations', function (Builder $query) use ($args) {
                $query->where('partner_locations.id', $args['placeId']);
            });
    }
}
