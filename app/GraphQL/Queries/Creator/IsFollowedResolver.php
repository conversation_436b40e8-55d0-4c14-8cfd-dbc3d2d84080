<?php

namespace App\GraphQL\Queries\Creator;

use App\Models\Creator;
use App\Models\User;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class IsFollowedResolver
{
    /**
     * Resolve whether the logged-in user follows the creator.
     *
     * @param  array<string, mixed>  $args
     */
    public function __invoke(Creator $creator, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): bool
    {
        /** @var ?User $user */
        $user = $context->user();

        // If no user is authenticated, they're not following anyone
        if (! $user) {
            return false;
        }

        // Check if an authenticated user follows this creator
        return $creator->following_users()
            ->where('user_id', $user->id)
            ->exists();
    }
}
