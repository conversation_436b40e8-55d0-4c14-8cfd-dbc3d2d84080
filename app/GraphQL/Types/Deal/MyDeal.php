<?php

declare(strict_types=1);

namespace App\GraphQL\Types\Deal;

use App\Models\Deal;
use App\Models\User;
use App\Models\UserDeal;
use Illuminate\Database\Eloquent\Builder;
use Nuwave\Lighthouse\Execution\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class MyDeal
{
    public function __invoke(mixed $root, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): mixed
    {
        /** @var Deal|null $root */
        /** @var User $user */
        $user = $context->user();

        return UserDeal::query()
            ->where('user_id', $user->id)
            ->when($root?->id, fn (Builder $query) => $query->where('deal_id', $root->id))
            ->when(isset($args['orderBy']), function (Builder $query) use ($args) {
                foreach ($args['orderBy'] as $order) {
                    if (str_contains($order['column'], 'reserve_slot') !== false) {
                        $query->orderByRaw("JSON_EXTRACT({$order['column']}, '$.date') {$order['order']}");
                    } else {
                        $query->orderBy($order['column'], $order['order']);
                    }
                }
            });
    }
}
