<?php

declare(strict_types=1);

namespace App\GraphQL\Types\Creator;

use App\Models\Creator;
use App\Models\PartnerLocation;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Nuwave\Lighthouse\Execution\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

final readonly class PlacesCount
{
    public function __invoke(Creator $root, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): mixed
    {
        /** @var User $user */
        $user = $context->user();

        return PartnerLocation::query()
            ->whereHas('reels', fn (Builder $query) => $query->where('creatable_type', $root->getMorphClass())->where('creatable_id', $root->id))
            ->count();
    }
}
