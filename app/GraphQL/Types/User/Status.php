<?php

declare(strict_types=1);

namespace App\GraphQL\Types\User;

use App\Enums\SocialLoginStatus;
use App\Models\Invitation;
use App\Models\User;

final readonly class Status
{
    /** @param  array{}  $args */
    public function __invoke(?User $user, array $args): SocialLoginStatus
    {
        if (! $user) {
            return SocialLoginStatus::SUCCESS;
        }

        $invitation = Invitation::where('email', $user->email)->first();

        if (! $invitation) {
            return SocialLoginStatus::SUCCESS;
        }

        if ($invitation->isExpired()) {
            return SocialLoginStatus::EXPIRED_INVITATION;
        }

        if (! $invitation->isApproved()) {
            return SocialLoginStatus::AWAITING_APPROVAL;
        }

        return SocialLoginStatus::SUCCESS;
    }
}
