<?php

declare(strict_types=1);

namespace App\GraphQL\Types\User;

use App\Models\User;

final readonly class Invitations
{
    /** @param  array{}  $args */
    public function __invoke(?User $user, array $args): array
    {
        if (! $user) {
            return [
                'data' => [],
                'info' => [
                    'total' => 0,
                    'remaining' => 0,
                ],
            ];
        }

        $invitations = $user->sentInvitations()
            ->with(['invitee'])
            ->orderBy('created_at', 'desc')
            ->get();

        $invitationInfo = $user->getInvitationInfo();

        return [
            'data' => $invitations,
            'info' => $invitationInfo,
        ];
    }
}
