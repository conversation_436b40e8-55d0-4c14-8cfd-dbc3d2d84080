<?php

namespace App\GraphQL\Resolvers\Collection;

use App\Models\Collection;
use App\Models\User;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class IsItemExistsResolver
{
    /**
     * Resolve the is_item_exists field for a Collection.
     *
     * @param  Collection  $collection  The parent collection object.
     * @param  array  $args  The arguments passed to the field.
     * @param  GraphQLContext  $context  The GraphQL context.
     * @param  ResolveInfo  $resolveInfo  The resolve info.
     */
    public function __invoke(Collection $collection, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): bool
    {
        $collectable_id = $args['collectable_id'] ?? null;
        $collectable_type_input = $args['collectable_type'] ?? null;

        /** @var User|null $user */
        $user = Auth::user();

        if (! $user || $collectable_id === null || $collectable_type_input === null) {
            // If user is not authenticated, or if essential arguments are missing, then return false.
            return false;
        }

        $modelClass = match (strtoupper($collectable_type_input)) {
            'REEL' => \App\Models\Reel::class,
            'CREATOR' => \App\Models\Creator::class,
            'PARTNER_LOCATION' => \App\Models\PartnerLocation::class,
            default => null,
        };

        if ($modelClass === null) {
            // Unknown collectable type
            return false;
        }

        // Check if the item exists in the current collection for the authenticated user.
        // This assumes the Collection model ($collection) is already scoped to the user
        // or that user ownership is checked at a higher level (e.g., in the main query).
        // We also explicitly check user_id on the collection for safety.
        if ($collection->user_id !== $user->id) {
            // This should ideally not happen if collections are correctly fetched for the user.
            return false;
        }

        return $collection->items()
            ->where('collectable_id', $collectable_id)
            ->where('collectable_type', $modelClass)
            ->exists();
    }
}
