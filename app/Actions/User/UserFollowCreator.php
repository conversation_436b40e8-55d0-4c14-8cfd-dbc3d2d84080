<?php

namespace App\Actions\User;

use App\Models\Creator;
use App\Models\User;

class UserFollowCreator
{
    public static function handle(User $user, int $creatorId): array
    {
        $user->following_creators()->toggle([
            $creatorId,
        ]);

        return [
            'status' => true,
            'message' => $user->following_creators()->where('creator_id', $creatorId)->exists()
                ? 'Creator followed'
                : 'Creator unfollowed',
            'creator' => Creator::query()->find($creatorId),
        ];
    }
}
