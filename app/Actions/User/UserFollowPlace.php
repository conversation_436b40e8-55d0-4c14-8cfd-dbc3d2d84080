<?php

namespace App\Actions\User;

use App\Models\PartnerLocation;
use App\Models\User;

class UserFollowPlace
{
    public static function handle(User $user, int $placeId): array
    {
        $user->following_places()->toggle([
            $placeId,
        ]);

        return [
            'status' => true,
            'message' => $user->following_places()->where('place_id', $placeId)->exists()
                ? 'Place followed'
                : 'Place unfollowed',
            'place' => PartnerLocation::query()->find($placeId),
        ];
    }
}
