<?php

namespace App\Actions\User;

use App\Models\Creator;
use App\Models\Like;
use App\Models\PartnerLocation;
use App\Models\Reel;
use App\Models\User;

class UserLike
{
    public static function handle(User $user, PartnerLocation|Creator|Reel $likable): array
    {
        $existing = Like::query()->where('user_id', $user->id)
            ->where('likeable_id', $likable->id)
            ->where('likeable_type', $likable->getMorphClass())
            ->first();

        $message = 'unLiked Successfully';

        if ($existing) {
            $existing->delete();
        } else {
            $message = 'Liked Successfully';

            Like::create([
                'user_id' => $user->id,
                'likeable_id' => $likable->id,
                'likeable_type' => $likable->getMorphClass(),
            ]);
        }

        return [
            'status' => true,
            'message' => $message,
            'likeable' => $likable,
        ];
    }
}
