<?php

namespace App\Actions\Deal;

use App\Models\User;
use App\Models\UserDeal;
use Carbon\Carbon;

class CancelDeal
{
    public static function handle(string|int $userDealId): string
    {
        /** @var User $user */
        $user = auth()->user();

        $userDeal = UserDeal::query()
            ->where('user_id', $user->id)
            ->where('id', $userDealId)
            ->first();

        if (! $userDeal) {
            throw new \GraphQL\Error\UserError('You do not have permission to cancel this deal.');
        }

        if ($userDeal->redeemed_at || Carbon::parse($userDeal->reserve_slot['date'])->isPast()) {
            throw new \GraphQL\Error\UserError(
                $userDeal->redeemed_at
                    ? 'Deal is already redeemed.'
                    : 'Only upcoming deals can be canceled'
            );
        }

        $isDeleted = $userDeal->delete();

        if (! $isDeleted) {
            throw new \GraphQL\Error\UserError('Failed to cancel deal');
        }

        return 'Deal cancelled successfully';
    }
}
