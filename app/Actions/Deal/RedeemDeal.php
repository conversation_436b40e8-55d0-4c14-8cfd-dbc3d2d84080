<?php

namespace App\Actions\Deal;

use App\Models\User;
use App\Models\UserDeal;
use Carbon\Carbon;

class RedeemDeal
{
    public static function handle(string|int $userDealId): UserDeal
    {
        /** @var User $user */
        $user = auth()->user();

        $userDeal = UserDeal::query()->where('user_id', $user->id)->where('id', $userDealId)->whereHas('deal')->first();

        if (! $userDeal) {
            throw new \GraphQL\Error\UserError('You do not have permission to redeem this deal.');
        }

        if (! Carbon::parse($userDeal->reserve_slot['date'])->isToday()) {
            throw new \GraphQL\Error\UserError('The reserve slot is not today');
        }

        // TODO: Check if time slot is now

        if ($userDeal->redeemed_at) {
            return $userDeal;
        }

        // update UserDeal status to redeemable and set redeemed_at to now
        $isUpdated = $userDeal->state()->redeem();

        if (! $isUpdated) {
            throw new \GraphQL\Error\UserError('Failed to redeem deal');
        }

        return $userDeal;
    }
}
