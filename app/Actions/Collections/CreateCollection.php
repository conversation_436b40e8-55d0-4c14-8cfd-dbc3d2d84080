<?php

namespace App\Actions\Collections;

use App\Models\Collection;
use App\Models\User;

class CreateCollection
{
    /**
     * Create a new collection and attach the user as OWNER.
     */
    public function handle(User $user, array $data): Collection
    {
        /** @var Collection $collection */
        $collection = $user->ownedCollections()->firstOrCreate([
            'user_id' => $user->id,
            'title' => $data['title'],
            'description' => $data['description'] ?? null,
        ]);

        if ($collection->wasRecentlyCreated) {
            $collection->users()->attach($user->id, ['role' => 'OWNER']);
        }

        return $collection;
    }
}
