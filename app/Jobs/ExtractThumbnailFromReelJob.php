<?php

namespace App\Jobs;

use App\Models\Partner;
use App\Models\Reel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use ProtoneMedia\LaravelFFMpeg\Support\FFMpeg;

class ExtractThumbnailFromReelJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(private readonly Reel $reel) {}

    public function handle(): void
    {
        /** @var Partner $partner */
        $partner = $this->reel->partner;

        FFMpeg::fromDisk('s3')
            ->open($this->reel->url)
            ->getFrameFromSeconds(1)
            ->export()
            ->toDisk('s3')
            ->withVisibility('public')
            ->save("reels/{$partner->id}/{$this->reel->id}/thumbnail.jpg");

        $this->reel->update([
            'thumbnail' => "reels/{$partner->id}/{$this->reel->id}/thumbnail.jpg",
        ]);
    }
}
