<?php

namespace App\StateManagers\Deal;

use App\Models\UserDeal;

class BaseDealStateManager implements DealStateManager
{
    public function __construct(
        public UserDeal $deal,
    ) {}

    public function markAsRedeemable()
    {
        throw new \Exception('Invalid action');
    }

    public function redeem(): bool
    {
        throw new \Exception('Invalid action');
    }

    public function markAsUpcoming()
    {
        throw new \Exception('Invalid action');
    }

    public function markAsNoShow()
    {
        throw new \Exception('Invalid action');
    }
}
