<?php

namespace App\Services;

use Firebase\JWT\JWT;

class GenerateAppleToken
{
    public static function generate(): string
    {
        $key = file_get_contents(base_path(config('services.apple.private_key')));

        $payload = [
            'iss' => config('services.apple.team_id'),
            'iat' => time(),
            'exp' => time() + (60 * 60), // 1 hour
            'aud' => 'https://appleid.apple.com',
            'sub' => config('services.apple.client_id'),
        ];

        return JWT::encode($payload, $key, 'ES256', config('services.apple.key_id'));
    }
}
