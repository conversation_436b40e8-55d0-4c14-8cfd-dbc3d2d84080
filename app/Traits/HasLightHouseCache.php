<?php

namespace App\Traits;

use Illuminate\Support\Facades\Cache;

trait HasLightHouseCache
{
    public function clearLightHouseCaches(): void
    {
        foreach ($this->getCacheKeys() as $cacheKey) {
            if ($fullKey = $this->generateQueryCacheFullKey($cacheKey)) {
                Cache::forget(
                    $fullKey
                );
            }

            if ($fullPrivateKey = $this->generatePrivateQueryCacheFullKey($cacheKey)) {
                Cache::forget(
                    $fullPrivateKey
                );
            }
        }
    }

    public function generateQueryCacheFullKey(string $identifier): string
    {
        return "lighthouse:Query::$identifier:{$this->getKey()}";
    }

    public function generatePrivateQueryCacheFullKey(string $identifier): ?string
    {
        $auth = auth()->user();

        if ($auth) {
            return "lighthouse:auth:{$auth->getKey()}:Query::$identifier:{$this->getKey()}";
        }

        return null;
    }
}
