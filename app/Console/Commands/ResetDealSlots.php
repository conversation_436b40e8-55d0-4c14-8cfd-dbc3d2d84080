<?php

namespace App\Console\Commands;

use App\Models\Deal;
use App\Models\DealSlot;
use Illuminate\Console\Command;

class ResetDealSlots extends Command
{
    protected $signature = 'deals:reset-slots';

    protected $description = 'Delete all deal slots and create default slots for each deal';

    public function handle()
    {
        $this->components->info('Deleting all existing deal slots...');

        DealSlot::truncate();

        $this->components->info('All deal slots deleted.');

        $deals = Deal::all();

        $totalDeals = $deals->count();

        $this->components->info("Creating default slots for {$totalDeals} deals...");

        foreach ($deals as $deal) {
            // Create slots for 7 days
            for ($day = 0; $day < 7; $day++) {
                // Slot 1: 9:00 to 15:00
                DealSlot::query()->create([
                    'deal_id' => $deal->id,
                    'day' => $day,
                    'from' => '09:00',
                    'to' => '15:00',
                ]);

                // Slot 2: 15:00 to 18:00
                DealSlot::query()->create([
                    'deal_id' => $deal->id,
                    'day' => $day,
                    'from' => '15:00',
                    'to' => '18:00',
                ]);
            }

        }

        $this->newLine();

        $this->components->task('Default slots created successfully!');

        $this->newLine();

        return self::SUCCESS;
    }
}
