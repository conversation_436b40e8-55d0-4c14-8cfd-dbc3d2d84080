<?php

namespace App\Console\Commands;

use App\Jobs\ExtractThumbnailFromReelJob;
use Illuminate\Console\Command;

class CreateThumbnailsForReelsCommand extends Command
{
    protected $signature = 'create:thumbnails-for-reels';

    protected $description = 'Create a thumbnails for all reels';

    public function handle(): int
    {
        $reels = \App\Models\Reel::query()->whereNull('thumbnail')->get();

        foreach ($reels as $reel) {
            ExtractThumbnailFromReelJob::dispatch($reel);
        }

        return self::SUCCESS;
    }
}
