<?php

namespace App\Console\Commands\Deal;

use App\Models\UserDeal;
use Carbon\Carbon;
use Illuminate\Console\Command;

class MarkDealsAsUpComingCommand extends Command
{
    protected $signature = 'deal:mark-deals-as-available';

    protected $description = 'Command description';

    public function handle(): int
    {
        $deals = UserDeal::query()
            ->whereHas('deal')
            ->whereHas('user')
            ->whereIn('status', ['redeemed', 'no-show'])
            ->whereDate('reuse_after', '<=', now()->toDateString())
            ->get();

        foreach ($deals as $deal) {
            if ($deal->reserve_slot['slot']['from'] && $deal->reserve_slot['slot']['to']) {
                $from = Carbon::parse($deal->reserve_slot['slot']['from']);
                $to = Carbon::parse($deal->reserve_slot['slot']['to']);

                $deal->state()->markAsUpcoming();

                if (now()->isBetween($from, $to)) {
                    $deal->state()->markAsRedeemable();
                }
            }
        }

        return self::SUCCESS;
    }
}
