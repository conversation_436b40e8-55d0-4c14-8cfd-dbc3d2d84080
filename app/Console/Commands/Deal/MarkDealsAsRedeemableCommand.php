<?php

namespace App\Console\Commands\Deal;

use App\Models\UserDeal;
use Carbon\Carbon;
use Illuminate\Console\Command;

class MarkDealsAsRedeemableCommand extends Command
{
    protected $signature = 'deal:mark-deals-as-redeemable';

    protected $description = 'Command description';

    public function handle(): int
    {

        $deals = UserDeal::query()
            ->whereHas('deal')
            ->whereHas('user')
            ->where('status', 'upcoming')
            ->whereJsonContains('reserve_slot->date', now()->toDateString())
            ->get();

        foreach ($deals as $deal) {
            if ($deal->reserve_slot['slot']['from'] && $deal->reserve_slot['slot']['to']) {
                $from = Carbon::parse($deal->reserve_slot['slot']['from']);
                $to = Carbon::parse($deal->reserve_slot['slot']['to']);

                if (now()->isBetween($from, $to)) {
                    $deal->state()->markAsRedeemable();
                }
            }
        }

        return self::SUCCESS;
    }
}
