<?php

namespace App\Console\Commands\Deal;

use App\Models\UserDeal;
use Illuminate\Console\Command;

class MarkDealsAsNoShowCommand extends Command
{
    protected $signature = 'deal:mark-deals-as-no-show';

    protected $description = 'Command description';

    public function handle(): int
    {

        $deals = UserDeal::query()
            ->whereHas('deal')
            ->whereHas('user')
            ->whereIn('status', ['upcoming', 'redeemable'])
            ->where('reserve_slot->date', '<=', now()->toDateString())
            ->get();

        foreach ($deals as $deal) {
            if ($deal->reserve_slot['slot']['from'] && $deal->reserve_slot['slot']['to']) {
                if (now()->gt($deal->reserve_slot['slot']['to'])) {
                    $deal->state()->markAsNoShow();
                }
            }
        }

        return self::SUCCESS;
    }
}
