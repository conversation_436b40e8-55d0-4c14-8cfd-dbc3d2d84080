<?php

namespace App\Console\Commands\Collection;

use App\Models\CollectionItem;
use App\Models\Creator;
use App\Models\PartnerLocation;
use App\Models\Reel;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CleanOrphanCollectionItemsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'collection:clean-orphans';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deletes CollectionItems where the underlying collectable model no longer exists.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting to clean orphan CollectionItems...');

        $orphan_count = 0;
        $total_checked = 0;

        // Define the collectable types and their corresponding models
        $collectable_types = [
            // Add other collectable types here as needed
            // 'App\\Models\\YourModel' => YourModel::class,
            'App\\Models\\PartnerLocation' => PartnerLocation::class,
            'App\\Models\\Creator' => Creator::class,
            'App\\Models\\Reel' => Reel::class,
        ];

        CollectionItem::query()
            ->select('id', 'collectable_id', 'collectable_type') // Select only necessary fields
            ->chunkById(200, function ($collection_items) use (&$orphan_count, &$total_checked, $collectable_types) {
                foreach ($collection_items as $item) {
                    $total_checked++;
                    $model_class = $item->collectable_type;

                    if (! isset($collectable_types[$model_class])) {
                        $this->warn("Unknown collectable_type: {$model_class} for CollectionItem ID: {$item->id}. Skipping.");

                        continue;
                    }

                    // Check if the related model exists
                    $related_exists = DB::table(app($collectable_types[$model_class])->getTable())
                        ->where('id', $item->collectable_id)
                        ->exists();

                    if (! $related_exists) {
                        $this->line("Found orphan CollectionItem ID: {$item->id} (Type: {$item->collectable_type}, Collectable ID: {$item->collectable_id}). Deleting...");
                        $item->delete();
                        $orphan_count++;
                    }
                }
            });

        $this->info("Checked {$total_checked} CollectionItems.");
        $this->info("Successfully deleted {$orphan_count} orphan CollectionItems.");

        return self::SUCCESS;
    }
}
