# .cursorrules

# You are developing Conari: a Laravel-based food content platform
# for short-form food videos (reels), restaurant discovery, and exclusive deals.
# Backend Stack:
# - <PERSON><PERSON> (latest)
# - Lighthouse GraphQL
# - FilamentPHP Admin
# - Algolia Search

# === Principles ===
principles:
  - Follow <PERSON><PERSON> and <PERSON><PERSON> best practices.
  - Write expressive, SOLID, testable code.
  - Favor dependency injection and service-based architecture.
  - Use Laravel's features: Eloquent, Form Requests, Policies, Events, Jobs.
  - API powered by Lighthouse GraphQL.
  - Search powered by Algolia with geo-awareness.

# === PHP & Laravel Conventions ===
php_laravel:
  version: 8.4
  strict_typing: true
  psr: psr-12
  error_handling:
    - Use try/catch when needed
    - Prefer <PERSON><PERSON>'s exception handler
    - Create domain-specific exceptions
  validation: form_requests
  routing: route_resource_api
  relationships: eloquent_relationships
  structure:
    - GraphQL: app/GraphQL/{Queries,Mutations,Resolvers}
    - Models: app/Models
    - Use lowercase-dash folders for consistency

# === GraphQL Cursor Rules ===
graphql:
  pagination:
    type: cursor
    default_limit: 20
    max_limit: 50
    cursor_field: created_at
    cursor_encoding: base64
    includes:
      - hasNextPage
      - hasPreviousPage
      - startCursor
      - endCursor

  queries:
    reels:
      model: App\Models\Reel
      order_by: created_at
      searchable: true
      indexed_by: algolia
      filters:
        - cuisine
        - duration
        - creator_id
      relations:
        - user
        - likes
        - comments

    restaurants:
      model: App\Models\Restaurant
      order_by: name
      searchable: true
      indexed_by: algolia
      filters:
        - cuisine
        - city
        - location (geo-aware)

    deals:
      model: App\Models\Deal
      order_by: valid_until
      searchable: true
      indexed_by: algolia
      filters:
        - restaurant_id
        - is_active
        - type

    bookmarks:
      model: App\Models\Bookmark
      order_by: created_at
      auth_required: true

    collections:
      model: App\Models\Collection
      order_by: updated_at
      auth_required: true

    creators:
      model: App\Models\User
      order_by: followers_count
      role: creator
      searchable: true

# === Admin Rules (Filament) ===
admin:
  panel: filamentphp
  resources:
    - ReelResource
    - DealResource
    - RestaurantResource
    - UserResource
  features:
    - Impersonation
    - Inline editing
    - Filters
    - Searchable tables
    - CSV Export
    - Actions: Approve, Feature, Ban

# === Search Rules (Algolia) ===
search:
  provider: algolia
  indexables:
    - App\Models\Reel
    - App\Models\Deal
    - App\Models\Restaurant
    - App\Models\User
  geo_support: true
  reindex_strategy: observer_based