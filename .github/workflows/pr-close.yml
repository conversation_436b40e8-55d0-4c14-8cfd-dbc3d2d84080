name: Cleanup PR

on:
  pull_request:
    types: [closed]

  # dispatch event to trigger the workflow manually with input PR Number
  workflow_dispatch:
    inputs:
      pr_number:
        description: "Pull Request Number"
        required: true

permissions:
  contents: read
  pull-requests: read

jobs:
  cleanup:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.4
          tools: composer:v2
          coverage: none

      - name: Require Vapor CLI
        run: composer global require laravel/vapor-cli --quiet

      - name: Create Vapor env file
        run: |
          sed -i 's/staging:/pr-${{ github.event.pull_request.number || inputs.pr_number }}:/g' vapor.yml

      - name: Delete Vapor Environment
        run: |
          yes | vapor env:delete pr-${{ github.event.pull_request.number || inputs.pr_number }}
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: extract branch name
        if: github.event.pull_request.number
        id: get_branch
        shell: bash
        env:
          PR_HEAD: ${{ github.head_ref }}
        run: echo "branch=$(echo ${PR_HEAD#refs/heads/} | tr / -)" >> $GITHUB_ENV

      # - name: mark environment as deactivated
      #   if: github.event.pull_request.number
      #   uses: bobheadxi/deployments@v1
      #   with:
      #     step: deactivate-env
      #     token: ${{ secrets.GITHUB_TOKEN }}
      #     env: ${{ steps.get_branch.outputs.branch }}
      #     desc: Environment was pruned
