name: Deploy Production

on:
  release:
    types: [created]
  workflow_dispatch:

permissions:
  contents: write
  actions: read
  id-token: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64

      - name: Add Apple Private Key file
        run: |
          cat << EOF > AuthKey_J97RP548YY.p8
          ${{ secrets.APPLE_PRIVATE_KEY }}
          EOF

      - name: Add Firebase Key file
        run: |
          cat << EOF > firebase-credentials.json
          ${{ secrets.FIREBASE_CREDENTIALS }}
          EOF

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.4
          tools: composer:v2
          coverage: none

      - name: Get Composer Cache Directory 📂
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer ➕
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install composer dependencies 🤖
        run: composer install --no-dev --no-progress --prefer-dist --no-interaction --no-suggest --optimize-autoloader --quiet

      - name: Require Vapor CLI
        run: composer global require laravel/vapor-cli --quiet

      - name: Deploy Environment
        run: vapor deploy production --commit="${{ github.sha }}" --manifest=vapor-prod.yml
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Upload Failure Artifacts 😰
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: Logs
          path: ./storage/logs

  Sentry:
    runs-on: ubuntu-latest
    needs: deploy
    permissions:
      contents: read
    steps:
      - name: Create Sentry release
        uses: getsentry/action-release@v3
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: tcf-sala
          SENTRY_PROJECT: conari
        with:
          environment: staging
