name: Deploy PR

on: pull_request

permissions:
  contents: read
  deployments: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      # - name: extract branch name
      #   id: get_branch
      #   shell: bash
      #   env:
      #     PR_HEAD: ${{ github.head_ref }}
      #   run: echo "##[set-output name=branch;]$(echo ${PR_HEAD#refs/heads/} | tr / -)"

      # - name: start deployment
      #   uses: bobheadxi/deployments@v1
      #   id: deployment
      #   with:
      #     step: start
      #     token: ${{ secrets.GITHUB_TOKEN }}
      #     env: ${{ steps.get_branch.outputs.branch }}
      #     ref: ${{ github.head_ref }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64

      - name: Add Apple Private Key file
        run: |
          cat << EOF > AuthKey_J97RP548YY.p8
          ${{ secrets.APPLE_PRIVATE_KEY }}
          EOF

      - name: Add Firebase Key file
        run: |
          cat << EOF > firebase-credentials.json
          ${{ secrets.FIREBASE_CREDENTIALS_DEVELOPMENT }}
          EOF

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.4
          tools: composer:v2
          coverage: none

      - name: Get Composer Cache Directory 📂
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer ➕
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Require Vapor CLI
        run: composer global require laravel/vapor-cli --quiet

      - name: Install composer dependencies 🤖
        run: composer install --no-progress --prefer-dist --no-interaction --no-suggest --optimize-autoloader --quiet

      - name: Prepare Vapor PR files
        run: |
          sed -i 's/staging:/pr-${{ github.event.pull_request.number }}:/g' vapor.yml
          sed -i 's/conari.co/pr-${{ github.event.pull_request.number }}.conari.co/g' vapor.yml

      - name: Create Vapor Environment
        continue-on-error: true
        run: |
          vapor env:clone staging pr-${{ github.event.pull_request.number }}
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Deploy Environment
        run: vapor deploy pr-${{ github.event.pull_request.number }} --commit="${{ github.sha }}"
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Upload Failure Artifacts 😰
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: Logs
          path: ./storage/logs

      # - name: Update Backend Deployment Link Status
      #   if: always()
      #   uses: bobheadxi/deployments@v1
      #   with:
      #     step: finish
      #     env_url: https://pr-${{ github.event.pull_request.number }}.conari.co
      #     token: ${{ secrets.GITHUB_TOKEN }}
      #     env: ${{ steps.deployment.outputs.env }}
      #     status: ${{ job.status }}
      #     logs: https://pr-${{ github.event.pull_request.number }}.conari.co
      #     deployment_id: ${{ steps.deployment.outputs.deployment_id }}
