name: Deploy Staging

permissions:
  contents: read
  deployments: write
  id-token: write

on:
  push:
    branches: [develop]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      # - name: start deployment
      #   uses: bobheadxi/deployments@v1
      #   id: deployment
      #   with:
      #     step: start
      #     token: ${{ secrets.GITHUB_TOKEN }}
      #     env: staging

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64

      - name: Add Apple Private Key file
        run: |
          cat << EOF > AuthKey_J97RP548YY.p8
          ${{ secrets.APPLE_PRIVATE_KEY }}
          EOF

      - name: Add Firebase Key file
        run: |
          cat << EOF > firebase-credentials.json
          ${{ secrets.FIREBASE_CREDENTIALS_DEVELOPMENT }}
          EOF

      # Replace scheduler: false in vapor.yml to scheduler: true
      - name: Enable Scheduler
        run: |
          sed -i 's/scheduler: false/scheduler: true/g' vapor.yml

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.4
          tools: composer:v2
          coverage: none

      - name: Get Composer Cache Directory 📂
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer ➕
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install composer dependencies 🤖
        run: composer install --no-dev --no-progress --prefer-dist --no-interaction --no-suggest --optimize-autoloader --quiet

      - name: Require Vapor CLI
        run: composer global require laravel/vapor-cli --quiet

      - name: Deploy Environment
        run: vapor deploy staging --commit="${{ github.sha }}"
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Upload Failure Artifacts 😰
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: Logs
          path: ./storage/logs

      # - name: update deployment status
      #   uses: bobheadxi/deployments@v1
      #   if: always()
      #   with:
      #     step: finish
      #     token: ${{ secrets.GITHUB_TOKEN }}
      #     status: ${{ job.status }}
      #     env: ${{ steps.deployment.outputs.env }}
      #     deployment_id: ${{ steps.deployment.outputs.deployment_id }}
