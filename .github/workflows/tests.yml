name: Tests

on:
  pull_request:
  push:
    branches:
      - develop
      - master

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.4
          tools: composer:v2
          coverage: xdebug

      - name: Install Dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Copy .env file
        run: cp .env.example .env.testing

      - name: Generate App Key
        run: php artisan key:generate --env=testing

      - name: Tests
        run: ./vendor/bin/pest --ci --parallel --coverage-clover=coverage.xml

      - name: SonarQube Scan
        uses: SonarSource/sonarqube-scan-action@v5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
