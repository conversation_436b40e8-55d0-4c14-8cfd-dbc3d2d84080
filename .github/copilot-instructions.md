# Conari Backend - GitHub Copilot Instructions

## Project Overview

Conari is a Laravel-based platform (v11.x) that connects users, creators, and partners. It features social interactions like following and liking content, as well as deals and media content (reels).

## Tech Stack

- **Framework**: Laravel 11
- **API**: GraphQL with Lighthouse
- **Admin Panel**: Filament
- **Authentication**: Laravel Sanctum with Social Login support
- **Media Handling**: Spatie Media Library
- **Testing**: Pest
- **Static Analysis**: PHPStan
- **Deployment**: Laravel Vapor

## Coding Conventions

- Follow PSR-12 coding standards
- Use type declarations for method parameters and return types
- GraphQL schema is organized by domain (Users, Partners, Deals, etc.)
- GraphQL files are separated into Types, Queries, and Mutations
- Use Laravel's dependency injection wherever possible
- Implement interfaces for contracts in the Contracts directory
- Use Conventional Commits for branch name and pull request title

### Naming Convention

- File names: Use kebab-case (e.g., my-class-file.php)
- Class and Enum names: Use PascalCase (e.g., MyClass)
- Method names: Use camelCase (e.g., myMethod)
- Variable and Properties names: Use snake_case (e.g., my_variable)
- Constants and Enum Cases names: Use SCREAMING_SNAKE_CASE (e.g., MY_CONSTANT)

## Conventional Commits

- Follow the Conventional Commits specification for commit messages
- Use the following types: `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `build`, `ci`, `chore`, `revert`
- Format: `type(scope): description` (e.g., `feat(auth): add social login with Apple`)
- Use scopes based on application domains (e.g., `auth`, `deals`, `users`, `media`)
- Write descriptions in imperative, present tense (e.g., "add" not "added" or "adds")
- Reference issue numbers in the footer when applicable (e.g., `Fixes #123`)
- For breaking changes, add `BREAKING CHANGE:` in the footer with a description

## Architecture

- **Actions**: Single-purpose classes for business logic (SocialLogin, Profile updates, etc.)
- **Models**: Eloquent models with relationships
- **GraphQL**: Schema defined in dedicated .graphql files
- **Services**: Reusable service classes
- **StateManagers**: Handle complex state transitions
- **Enums**: Use PHP 8.1+ enum types for constants

## Testing Guidelines

- Write feature tests for GraphQL endpoints
- Use Pest for testing syntax
- Mock external services when testing

## Common Patterns

- Use Laravel's validation rules for input validation
- GraphQL resolvers should be thin and delegate to Actions
- Media uploads go through Spatie Media Library
- Social login flows use SocialiteProviders

## Special Instructions

- When suggesting new features, follow the existing pattern of separating GraphQL schema into Types, Queries, and Mutations
- For authentication-related changes, ensure compatibility with both token-based auth and social login
- When modifying database schemas, always include corresponding migrations
- Follow existing patterns for GraphQL directive usage
