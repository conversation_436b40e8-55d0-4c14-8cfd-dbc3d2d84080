{"$schema": "https://getcomposer.org/schema.json", "name": "conari/backend", "type": "project", "version": "1.0.0", "description": "<PERSON>ari-backend", "keywords": ["laravel", "framework"], "license": "Copyright (c) 2025 Conari", "require": {"php": "^8.4", "algolia/algoliasearch-client-php": "^4.18", "awcodes/filament-table-repeater": "^3.1", "filament/filament": "^3.3", "filament/spatie-laravel-media-library-plugin": "^3.3", "itsgoingd/clockwork": "^5.3", "laravel/framework": "^11.44", "laravel/octane": "^2.8", "laravel/sanctum": "^4.0", "laravel/scout": "^10.14", "laravel/socialite": "^5.18", "laravel/tinker": "^2.10", "laravel/vapor-core": "^2.37", "league/flysystem-aws-s3-v3": "^3.29", "mll-lab/laravel-graphiql": "v4.0.2", "nuwave/lighthouse": "^6.54", "pbmedia/laravel-ffmpeg": "^8.7", "php-ffmpeg/php-ffmpeg": "^1.3", "sentry/sentry-laravel": "^4.13", "socialiteproviders/apple": "^5.6", "spatie/laravel-medialibrary": "^11.12"}, "require-dev": {"fakerphp/faker": "^1.24", "larastan/larastan": "^3.3", "laravel/breeze": "^2.3", "laravel/pail": "^1.2", "laravel/pint": "^1.21", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.7", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.1", "pestphp/pest-plugin-livewire": "^3.0", "pestphp/pest-plugin-watch": "^3.0", "spatie/laravel-ray": "^1.40"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "git config core.hooksPath .githooks", "@php artisan lighthouse:ide-helper", "@php artisan filament:upgrade", "@php artisan vendor:publish --tag=livewire:assets --ansi --force"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" --names=server,queue,logs,vite"], "lint": ["./vendor/bin/pint", "npx prettier . -w", "./vendor/bin/phpstan analyse --memory-limit=1024M"], "test": ["php artisan key:generate --env=testing && php artisan test --env=testing --parallel && git checkout .env.testing"], "test:watch": ["php artisan test --env=testing --watch"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}