<?php

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "pest()" function to bind a different classes or traits.
|
*/

use Illuminate\Testing\TestResponse;
use Laravel\Socialite\Two\User as SocialiteUser;

uses(\Nuwave\Lighthouse\Testing\MakesGraphQLRequests::class);

pest()->extend(Tests\TestCase::class)
    ->use(Illuminate\Foundation\Testing\RefreshDatabase::class)
    ->in('Feature');

/*
|--------------------------------------------------------------------------
| Expectations
|--------------------------------------------------------------------------
|
| When you're writing tests, you often need to check that values meet certain conditions. The
| "expect()" function gives you access to a set of "expectations" methods that you can use
| to assert different things. Of course, you may extend the Expectation API at any time.
|
*/

expect()->extend('toBeOne', function () {
    return $this->toBe(1);
});

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-box, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

function graphQl(
    string $query,
    array $variables = [],
    array $extraParams = [],
    array $headers = [],
    array $routeParams = [],
): TestResponse {
    /** @phpstan-ignore-next-line */
    return test()
        ->graphQL(...func_get_args());
}

function mockSocialiteDriver(
    string $provider,
    string $id = '123',
    string $email = '<EMAIL>',
    string $name = 'Test User',
    string $token = 'valid-token'
): void {
    $abstractUser = Mockery::mock(SocialiteUser::class);
    $abstractUser->shouldReceive('getId')
        ->andReturn($id);
    $abstractUser->shouldReceive('getEmail')
        ->andReturn($email);
    $abstractUser->shouldReceive('getName')
        ->andReturn($name);
    $abstractUser->shouldReceive('getNickname')
        ->andReturn($name);

    $providerMock = Mockery::mock('Laravel\\Socialite\\Two\\AbstractProvider');
    $providerMock->shouldReceive('userFromToken') // @phpstan-ignore-line
        ->andReturnUsing(function ($receivedToken) use ($token, $abstractUser) {
            if ($receivedToken !== $token) {
                throw new \InvalidArgumentException('Invalid token');
            }

            return $abstractUser;
        });

    Socialite::shouldReceive('driver')
        ->with(strtolower($provider))
        ->andReturn($providerMock);
}
