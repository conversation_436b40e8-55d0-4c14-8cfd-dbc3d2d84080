<?php

use App\Models\Employee;
use App\Models\PartnerLocation;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(Tests\TestCase::class, RefreshDatabase::class);

it('can attach places to employees', function () {
    // Create an employee
    /** @var Employee $employee */
    $employee = Employee::factory()->create();

    // Create places
    /** @var PartnerLocation $placeA */
    $placeA = PartnerLocation::factory()->create();
    /** @var PartnerLocation $placeB */
    $placeB = PartnerLocation::factory()->create();

    // Attach places to employee
    $employee->places()->attach([$placeA->id, $placeB->id]);

    // Check relationships
    expect($employee->places()->count())->toBe(2)
        ->and($employee->places->pluck('id')->toArray())->toContain($placeA->id, $placeB->id);
});

it('can detach places from employees', function () {
    // Create an employee
    /** @var Employee $employee */
    $employee = Employee::factory()->create();

    // Create places
    /** @var PartnerLocation $placeA */
    $placeA = PartnerLocation::factory()->create();
    /** @var PartnerLocation $placeB */
    $placeB = PartnerLocation::factory()->create();

    // Attach places to employee
    $employee->places()->attach([$placeA->id, $placeB->id]);

    // Detach one place
    $employee->places()->detach($placeA->id);

    // Reload the relationship
    $employee->load('places');

    // Check relationships
    expect($employee->places()->count())->toBe(1)
        ->and($employee->places->pluck('id')->toArray())->toContain($placeB->id)
        ->and($employee->places->pluck('id')->toArray())->not->toContain($placeA->id);
});

it('can find employees by place', function () {
    // Create employees
    /** @var Employee $employeeA */
    $employeeA = Employee::factory()->create();
    /** @var Employee $employeeB */
    $employeeB = Employee::factory()->create();

    // Create a place
    /** @var PartnerLocation $place */
    $place = PartnerLocation::factory()->create();

    // Attach the place to one employee
    $employeeA->places()->attach($place->id);

    // Check the place's employees relationship
    expect($place->employees()->count())->toBe(1)
        ->and($place->employees->pluck('id')->toArray())->toContain($employeeA->id)
        ->and($place->employees->pluck('id')->toArray())->not->toContain($employeeB->id);
});
