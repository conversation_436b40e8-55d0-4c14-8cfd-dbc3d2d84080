<?php

use App\Console\Commands\Deal\MarkDealsAsNoShowCommand;
use App\Models\UserDeal;

use function Pest\Laravel\artisan;

test('it can mark deals as no show', function () {
    \Carbon\Carbon::setTestNow(\Carbon\Carbon::parse(now()->toDateString().' 16:00'));

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::factory()->redeemable()->create([
        'reserve_slot' => [
            'date' => now()->toDateString(),
            'slot' => [
                'from' => '13:00',
                'to' => '15:00',
            ],
        ],
        'reuse_after' => now()->subDay()->toDateString(),
    ]);

    // Act
    artisan(MarkDealsAsNoShowCommand::class);

    // Assert
    expect($userDeal->fresh()->status)->toBe('no-show');

    \Carbon\Carbon::setTestNow(\Carbon\Carbon::parse(now()->toDateString().' 16:00'));

    artisan(\App\Console\Commands\Deal\MarkDealsAsUpComingCommand::class);

    expect($userDeal->fresh()->status)->toBe('upcoming');
});
