<?php

namespace Tests\Unit\Console\Commands\Deal;

use App\Console\Commands\Deal\MarkDealsAsRedeemableCommand;
use App\Models\UserDeal;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Laravel\artisan;

uses(RefreshDatabase::class);

test('it can mark deals as redeemable', function () {
    \Carbon\Carbon::setTestNow(\Carbon\Carbon::parse(now()->toDateString().' 14:00'));

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::factory()->redeemable()->create([
        'reserve_slot' => [
            'date' => now()->toDateString(),
            'slot' => [
                'from' => '13:00',
                'to' => '15:00',
            ],
        ],
        'reuse_after' => now()->subDay()->toDateString(),
    ]);

    // Act
    artisan(MarkDealsAsRedeemableCommand::class);

    // Assert
    expect($userDeal->fresh()->status)->toBe('redeemable');
});
