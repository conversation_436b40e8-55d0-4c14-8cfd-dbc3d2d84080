<?php

use App\Console\Commands\Deal\MarkDealsAsRedeemableCommand;
use App\Console\Commands\Deal\MarkDealsAsUpComingCommand;
use App\Models\UserDeal;

use function Pest\Laravel\artisan;

test('it can mark deals as upcoming', function () {
    // Arrange
    \Carbon\Carbon::setTestNow(\Carbon\Carbon::parse(now()->toDateString().' 12:00'));

    /** @var UserDeal $userDeal */
    $userDeal = UserDeal::factory()->create([
        'status' => 'redeemed',
        'reserve_slot' => [
            'date' => now()->toDateString(),
            'slot' => [
                'from' => '13:00',
                'to' => '15:00',
            ],
        ],
        'reuse_after' => now()->subDay(),
    ]);

    // Act
    artisan(MarkDealsAsUpComingCommand::class);

    // Assert
    expect($userDeal->fresh()->status)->toBe('upcoming');

    \Carbon\Carbon::setTestNow(\Carbon\Carbon::parse(now()->toDateString().' 14:00'));

    // Act
    artisan(MarkDealsAsRedeemableCommand::class);

    // Assert
    expect($userDeal->fresh()->status)->toBe('redeemable');
});
