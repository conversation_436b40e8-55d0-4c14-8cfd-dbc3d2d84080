<?php

test('partner place indexing structure is right', function () {
    /** @var \App\Models\PartnerLocation $partnerLocation */
    $partnerLocation = \App\Models\PartnerLocation::factory()->openingHours($day = (int) now()->addDay()->format('w'), '09:00', '10:00')->create();

    expect($partnerLocation->toSearchableArray())->toHaveKeys([
        'id',
        'name',
        'rates',
        'area',
        'retail_destination',
        'price_per_person',
    ]);

    /** @var array $partnerLocationData */
    $partnerLocationData = $partnerLocation->toSearchableArray();

    expect($partnerLocationData['id'])
        ->toBe((string) $partnerLocation->id)
        ->and($partnerLocationData['name'])
        ->toBe($partnerLocation->name)
        ->and($partnerLocationData['rates'])
        ->toBe([
            'google' => $partnerLocation->google_rate,
            'reviews_count' => (float) $partnerLocation->reviews_count,
        ])
        ->and($partnerLocationData['area'])
        ->toBe($partnerLocation->area->name)
        ->and($partnerLocationData['retail_destination'])
        ->toBe($partnerLocation->retail_destination->name)
        ->and($partnerLocationData['price_per_person'])
        ->toBe($partnerLocation->price_per_person)
        ->and($partnerLocationData['opening_hours'])
        ->toBeObject()
        ->and($partnerLocationData['opening_hours']->toArray())
        ->toBe([
            [
                'day' => $day,
                'from' => \Carbon\Carbon::now()->next($day)->setHour(9)->setMinute(0)->setSecond(0)->toDateTimeString(),
                'to' => Carbon\Carbon::now()->next($day)->setHour(10)->setMinute(0)->setSecond(0)->toDateTimeString(),
            ],
        ]);
});
