<?php

use App\Filament\Employee\Resources\PartnerPlaceResource\Pages\EditPartnerPlace;
use App\Filament\Employee\Resources\PartnerPlaceResource\Pages\ListPartnerPlaces;
use App\Models\Admin;
use App\Models\Employee;
use App\Models\PartnerLocation;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Livewire\livewire;

/**
 * @phpstan-import-type LivewireTestingResponse from \Pest\Livewire
 */
uses(RefreshDatabase::class);

it('employee can only see attached places in list view', function () {
    // Create an employee
    /** @var Employee $employee */
    $employee = Employee::factory()->create();

    // Create places
    /** @var PartnerLocation $attachedPlace */
    $attachedPlace = PartnerLocation::factory()->create();
    /** @var PartnerLocation $unattachedPlace */
    $unattachedPlace = PartnerLocation::factory()->create();

    // Attach one place to the employee
    $employee->places()->attach($attachedPlace->id);

    // Act as the employee
    actingAs($employee, 'employee');

    // Test list view
    $response = livewire(ListPartnerPlaces::class)
        ->assertSuccessful();

    // Should only see the attached place
    /** @phpstan-ignore-next-line */
    $response->assertCanSeeTableRecords([$attachedPlace])
        ->assertCanNotSeeTableRecords([$unattachedPlace]);
});

it('employee can edit attached places', function () {
    // Create an employee
    /** @var Employee $employee */
    $employee = Employee::factory()->create();

    // Create a place and attach it to the employee
    /** @var PartnerLocation $place */
    $place = PartnerLocation::factory()->create();
    $employee->places()->attach($place->id);

    // Act as the employee
    actingAs($employee, 'employee');

    // Test edit view - should load successfully
    livewire(EditPartnerPlace::class, [
        'record' => $place->id,
    ])->assertSuccessful();

    // Note: We don't test form submission here as it depends on the specific form fields
    // That would be covered by admin panel tests
});

it('employee cannot access unattached places', function () {
    // Create an employee
    /** @var Employee $employee */
    $employee = Employee::factory()->create();

    // Create a place but don't attach it to the employee
    /** @var PartnerLocation $place */
    $place = PartnerLocation::factory()->create();

    // Act as the employee
    actingAs($employee, 'employee');

    // Attempt to access the edit page
    get(EditPartnerPlace::getUrl(['record' => $place->id]))
        ->assertRedirect(); // Should redirect away
});

it('employee can see multiple attached places', function () {
    // Create an employee
    /** @var Employee $employee */
    $employee = Employee::factory()->create();

    // Create multiple places
    /** @var PartnerLocation $placeA */
    $placeA = PartnerLocation::factory()->create(['name' => 'Place A']);
    /** @var PartnerLocation $placeB */
    $placeB = PartnerLocation::factory()->create(['name' => 'Place B']);
    /** @var PartnerLocation $placeC */
    $placeC = PartnerLocation::factory()->create(['name' => 'Place C']);

    // Attach two places to the employee
    $employee->places()->attach([$placeA->id, $placeB->id]);

    // Act as the employee
    actingAs($employee, 'employee');

    // Test list view
    $response = livewire(ListPartnerPlaces::class)
        ->assertSuccessful();

    // Should see both attached places but not the unattached one
    /** @phpstan-ignore-next-line */
    $response->assertCanSeeTableRecords([$placeA, $placeB])
        ->assertCanNotSeeTableRecords([$placeC]);
});
