<?php

use App\Enums\InvitationStatus;
use App\Filament\Resources\InvitationResource;
use App\Filament\Resources\InvitationResource\Pages\ListInvitations;
use App\Models\Admin;
use App\Models\Invitation;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

it('lists all invitations', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    Invitation::factory()->count(3)->create();

    $response = livewire(ListInvitations::class)
        ->assertSuccessful();

    /** @phpstan-ignore-next-line */
    $response->assertCountTableRecords(3);
});

it('allows admin to approve invitation', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @var Invitation $invitation */
    $invitation = Invitation::factory()->pending()->create();

    /* @phpstan-ignore-next-line */
    livewire(ListInvitations::class)
        ->callTableAction('approve', $invitation)
        ->assertHasNoTableActionErrors();

    /** @var Invitation $invitation */
    $invitation = $invitation->fresh();

    expect($invitation->status)
        ->toBe(InvitationStatus::APPROVED);
});

it('allows admin to reject invitation', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @var Invitation */
    $invitation = Invitation::factory()->pending()->create();

    /* @phpstan-ignore-next-line */
    livewire(ListInvitations::class)
        ->callTableAction('reject', $invitation)
        ->assertHasNoTableActionErrors();

    /** @var Invitation $invitation */
    $invitation = $invitation->fresh();

    expect($invitation->status)
        ->toBe(InvitationStatus::REJECTED);
});

it('shows invitation status with appropriate colors', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @var Invitation $pendingInvitation */
    $pendingInvitation = Invitation::factory()->pending()->create();

    /** @var Invitation $approvedInvitation */
    $approvedInvitation = Invitation::factory()->approved()->create();

    /** @var Invitation $rejectedInvitation */
    $rejectedInvitation = Invitation::factory()->rejected()->create();

    /* @phpstan-ignore-next-line */
    livewire(ListInvitations::class)
        ->assertCanSeeTableRecords([$pendingInvitation, $approvedInvitation, $rejectedInvitation])
        ->assertSuccessful();
});

it('allows admin to create invitation', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    $expiresAt = now();
    $expireDays = (int) config('invitation.expiry_days');

    /* @phpstan-ignore-next-line */
    livewire(InvitationResource\Pages\CreateInvitation::class)
        ->fillForm([
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'provider' => 'google',
            'provider_id' => 'test-123',
            'status' => InvitationStatus::PENDING->value,
            'expires_at' => $expiresAt->copy()->addDays($expireDays),
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    $invitation = Invitation::latest('id')->first();

    expect($invitation)
        ->and($invitation->email)->toBe('<EMAIL>')
        ->and($invitation->name)->toBe('Test User')
        ->and($invitation->provider)->toBe('google')
        ->and($invitation->provider_id)->toBe('test-123')
        ->and($invitation->status)->toBe(InvitationStatus::PENDING)
        ->and($invitation->expires_at->toDateString())->toBe($expiresAt->addDays($expireDays)->toDateString());
});

it('prevents creating invitation with duplicate email', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @var Invitation */
    $existingInvitation = Invitation::factory()->create();

    /* @phpstan-ignore-next-line */
    livewire(InvitationResource\Pages\CreateInvitation::class)
        ->fillForm([
            'email' => $existingInvitation->email,
            'name' => 'Test User',
            'provider' => 'google',
            'provider_id' => 'test-123',
            'status' => InvitationStatus::PENDING->value,
            'expires_at' => now()->addDays((int) config('invitation.expiry_days')),
        ])
        ->call('create')
        ->assertHasFormErrors(['email']);
});
