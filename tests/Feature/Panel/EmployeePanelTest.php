<?php

use App\Filament\Employee\Resources\PartnerPlaceResource;
use App\Models\Employee;
use App\Models\PartnerLocation;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Laravel\actingAs;

uses(RefreshDatabase::class);

it('scopes partner places query to show only places attached to the employee', function () {
    // Create an employee
    /** @var Employee $employee */
    $employee = Employee::factory()->create();

    // Create places
    /** @var PartnerLocation $attachedPlace */
    $attachedPlace = PartnerLocation::factory()->create(['name' => 'Attached Place']);
    /** @var PartnerLocation $unattachedPlace */
    $unattachedPlace = PartnerLocation::factory()->create(['name' => 'Unattached Place']);

    // Attach one place to the employee
    $employee->places()->attach($attachedPlace->id);

    // Act as the employee
    actingAs($employee, 'employee');

    // Get the base query from the resource
    $query = PartnerPlaceResource::getEloquentQuery();

    /** @var PartnerLocation $firstInQuery */
    $firstInQuery = $query->first();

    // Verify it only returns the attached places
    expect($query->count())->toBe(1)
        ->and($firstInQuery->id)->toBe($attachedPlace->id)
        ->and($query->where('id', $unattachedPlace->id)->exists())->toBeFalse();
});

it('does not allow employees to create new places', function () {
    // Verify the canCreate method returns false
    expect(PartnerPlaceResource::canCreate())->toBeFalse();
});

it('handles employees with no attached places', function () {
    // Create an employee with no attached places
    /** @var Employee $employee */
    $employee = Employee::factory()->create();

    // Act as the employee
    actingAs($employee, 'employee');

    // Get the base query from the resource
    $query = PartnerPlaceResource::getEloquentQuery();

    // Verify it returns no places
    expect($query->count())->toBe(0);
});
