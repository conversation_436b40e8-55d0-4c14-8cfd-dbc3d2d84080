<?php

use App\Filament\Resources\PartnerPlaceResource\Pages\CreatePartnerPlace;
use App\Filament\Resources\PartnerPlaceResource\Pages\ListPartnerPlaces;
use App\Models\Admin;
use App\Models\Deal;
use App\Models\PartnerLocation;
use App\Models\Tag;
use Illuminate\Database\Eloquent\Collection;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

it('lists all partner places', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    PartnerLocation::factory()->create();

    $response = livewire(ListPartnerPlaces::class)
        ->assertSuccessful();

    /** @phpstan-ignore-next-line */
    $response->assertCountTableRecords(1);
});

it('creates a partner place', function () {
    \Carbon\Carbon::setTestNow(\Carbon\Carbon::parse('2025-04-16'));
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    $undoRepeater = \Filament\Forms\Components\Repeater::fake();

    /** @var array<string, mixed> */
    $locationData = PartnerLocation::factory()->raw([
        'name' => 'dddd',
        'ambiance' => Tag::inRandomOrder()->limit(3)->pluck('id')->toArray(),
        'opening_hours' => [
            [
                'day' => 0,
                'from' => '18:00',
                'to' => '02:00',
            ],
        ],
    ]);

    /** @var array<string, mixed> */
    $data = $locationData;
    $data['avatar'] = \Illuminate\Http\UploadedFile::fake()->image('avatar.jpg');
    $data['images'] = [
        \Illuminate\Http\UploadedFile::fake()->image('image.jpg'),
    ];

    $data['menu'] = [
        \Illuminate\Http\UploadedFile::fake()->image('menu.jpg'),
    ];

    /** @var Collection<Deal> */
    $deals = Deal::factory()->count(1)->make();

    $data['deals'] = $deals->toArray();
    $data['deals'][0][\App\Enums\TagType::SERVICE_OPTIONS->value] = Tag::inRandomOrder()->limit(3)->pluck('id')->toArray();

    foreach ($data['deals'][0]['valid_days'] as $day) {
        $data['deals'][0]['dealSlots'][$day] = [
            [
                'from' => '10:00',
                'to' => '20:00',
            ],
        ];
    }

    livewire(CreatePartnerPlace::class)// @phpstan-ignore-line
        ->fillForm($data)
        ->call('create')
        ->assertHasNoFormErrors();

    $undoRepeater();

    /** @var PartnerLocation */
    $location = PartnerLocation::query()->latest('id')->first();

    /** @var Deal */
    $deal = $location->deals()->first();
    // Assertions
    expect($location) // @phpstan-ignore-line
        ->name->toBe('dddd')
        ->lat->toEqual($locationData['lat'])
        ->lng->toEqual($locationData['lng'])
        ->address_line_1->toBe($locationData['address_line_1'])
        ->address_line_2->toBe($locationData['address_line_2'])
        ->city->toBe($locationData['city'])
        ->state->toBe($locationData['state'])
        ->postal_code->toBe($locationData['postal_code'])
        ->country->toBe($locationData['country'])
        ->and($location->media()->where('collection_name', 'menu')->count())
        ->toBe(1)
        ->and($location->opening_hours->toArray())
        ->toBe([
            [
                'day' => 0,
                'from' => '2025-04-20 18:00:00',
                'to' => '2025-04-21 02:00:00',
            ],
        ]);
});

it('duplicates a partner place with deals', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    $undoRepeater = \Filament\Forms\Components\Repeater::fake();

    /** @var PartnerLocation */
    $location = PartnerLocation::factory()->create([
        'name' => 'dddd',
    ]);

    $ambianceTags = Tag::query()->where('type', 'ambiance')->inRandomOrder()->limit(3)->pluck('id')->toArray();

    $location->tags()->attach($ambianceTags);

    livewire(ListPartnerPlaces::class)// @phpstan-ignore-line
        ->mountTableAction('custom_replicate', $location->id)
        ->assertTableActionDataSet([
            'lat' => null,
            'lng' => null,
            'google_rate' => $location->google_rate,
            'reviews_count' => $location->reviews_count,
            'ambiance' => $ambianceTags,
        ])
        ->setTableActionData([
            'name' => 'My Replica',
            'lat' => $newLat = fake()->latitude,
            'lng' => $newLng = fake()->longitude,
            'avatar' => \Illuminate\Http\UploadedFile::fake()->image('avatar.jpg'),
            'images' => [
                \Illuminate\Http\UploadedFile::fake()->image('image.jpg'),
            ],
        ])
        ->callMountedTableAction()
        ->assertHasNoTableActionErrors()
        ->assertSuccessful();

    $this->assertDatabaseCount(PartnerLocation::class, 2); // @phpstan-ignore-line
    $this->assertDatabaseHas(PartnerLocation::class, [// @phpstan-ignore-line
        'name' => 'My Replica',
        'lat' => $newLat,
        'lng' => $newLng,
        'google_rate' => $location->google_rate,
        'reviews_count' => $location->reviews_count,
    ]);

    /** @var PartnerLocation */
    $replica = PartnerLocation::query()->latest('id')->first();

    expect($replica->tags->pluck('pivot.tag_id')->toArray())
        ->toBe($ambianceTags);
});
