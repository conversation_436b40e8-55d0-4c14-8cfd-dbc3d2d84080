<?php

use App\Filament\Resources\CreatorResource\RelationManagers\ReelsRelationManager;
use App\Models\Creator;
use App\Models\Partner;
use App\Models\Reel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

uses(RefreshDatabase::class);

beforeEach(function () {
    Storage::fake('local');
});

it('lists creator reels in the relation manager', function () {
    /** @var Creator $creator */
    $creator = Creator::factory()->create();

    /** @var Partner $partner */
    $partner = Partner::factory()->create();

    /** @var Reel $reel */
    $reel = Reel::factory()->create([
        'partner_id' => $partner->id,
        'creatable_type' => Creator::class,
        'creatable_id' => $creator->id,
        'caption' => 'Test reel',
    ]);

    /** @var \App\Models\Admin $admin */
    $admin = \App\Models\Admin::factory()->create();
    actingAs($admin);

    livewire(ReelsRelationManager::class, [
        'ownerRecord' => $creator,
        'pageClass' => \App\Filament\Resources\CreatorResource\Pages\EditCreator::class,
    ])
        ->assertSuccessful()
        ->assertSeeText('Test reel');
});
