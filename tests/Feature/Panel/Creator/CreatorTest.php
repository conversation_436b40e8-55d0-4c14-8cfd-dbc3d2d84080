<?php

use function Pest\Laravel\actingAs;

it('lists all creators', function () {
    \App\Models\Creator::truncate();

    \App\Models\Creator::factory()->count(3)->create();

    /** @var \App\Models\Admin $admin */
    $admin = \App\Models\Admin::factory()->create();
    actingAs($admin);

    $response = \Pest\Livewire\livewire(\App\Filament\Resources\CreatorResource\Pages\ListCreators::class)
        ->assertSuccessful();

    /** @phpstan-ignore-next-line */
    $response->assertCountTableRecords(3);
});

it('creates a creator', function () {
    \App\Models\Creator::truncate();

    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    /** @var \App\Models\Admin $admin */
    $admin = \App\Models\Admin::factory()->create();
    actingAs($admin);

    \Pest\Livewire\livewire(\App\Filament\Resources\CreatorResource\Pages\CreateCreator::class)// @phpstan-ignore-line
        ->fillForm([
            'name' => 'test',
            'user_id' => $user->id,
            'bio' => 'test',
            'username' => 'test',
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    \Pest\Laravel\assertDatabaseHas(\App\Models\Creator::class, [
        'user_id' => $user->id,
        'bio' => 'test',
        'name' => 'test',
    ]);
});
