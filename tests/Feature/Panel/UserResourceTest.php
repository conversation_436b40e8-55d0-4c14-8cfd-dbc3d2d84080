<?php

use App\Filament\Resources\UserResource;
use App\Filament\Resources\UserResource\Pages\CreateUser;
use App\Filament\Resources\UserResource\Pages\EditUser;
use App\Filament\Resources\UserResource\Pages\ListUsers;
use App\Models\Admin;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

/**
 * @phpstan-import-type LivewireTestingResponse from \Pest\Livewire
 */
uses(RefreshDatabase::class);

it('lists all users', function () {
    User::truncate();

    User::factory()->count(3)->create();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    $response = livewire(ListUsers::class)
        ->assertSuccessful();

    /** @phpstan-ignore-next-line */
    $response->assertCountTableRecords(3);
});

it('creates a user', function () {
    User::truncate();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    $userData = [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'email_verified_at' => now(),
    ];

    /** @phpstan-ignore-next-line */
    livewire(CreateUser::class)
        ->fillForm($userData)
        ->call('create')
        ->assertHasNoFormErrors();

    // @phpstan-ignore-next-line
    $this->assertDatabaseHas(User::class, [
        'name' => 'Test User',
        'email' => '<EMAIL>',
    ]);

    /** @var User */
    $user = User::where('email', '<EMAIL>')->first();

    // @phpstan-ignore-next-line
    expect($user)
        ->name->toBe('Test User')
        ->email->toBe('<EMAIL>')
        ->and(Hash::check('password123', $user->password))->toBeTrue();
});

it('edits a user', function () {
    /** @var User */
    $user = User::factory()->create();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @phpstan-ignore-next-line */
    livewire(EditUser::class, [
        'record' => $user->id,
    ])
        ->assertFormSet([
            'name' => $user->name,
            'email' => $user->email,
        ])
        ->fillForm([
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'password' => 'newpassword123',
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    $user->refresh();

    // @phpstan-ignore-next-line
    expect($user)
        ->name->toBe('Updated Name')
        ->email->toBe('<EMAIL>')
        ->and(Hash::check('newpassword123', $user->password))->toBeTrue();
});

it('deletes a user', function () {
    /** @var User */
    $user = User::factory()->create();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @phpstan-ignore-next-line */
    livewire(ListUsers::class)
        ->callTableAction('delete', $user)
        ->assertSuccessful();

    // @phpstan-ignore-next-line
    $this->assertModelMissing($user);
});

it('bulk deletes users', function () {
    /** @var User */
    $userA = User::factory()->create();
    /** @var User */
    $userB = User::factory()->create();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @phpstan-ignore-next-line */
    livewire(ListUsers::class)
        ->callTableBulkAction('delete', [$userA->id, $userB->id])
        ->assertSuccessful();

    // @phpstan-ignore-next-line
    $this->assertModelMissing($userA);
    // @phpstan-ignore-next-line
    $this->assertModelMissing($userB);
});

it('cannot generate token for a user in production environment', function () {
    app()->detectEnvironment(fn () => 'production');

    /** @var User */
    $user = User::factory()->create();

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    livewire(ListUsers::class)
        ->assertTableActionDoesNotExist('generateToken');

    app()->detectEnvironment(fn () => 'testing');

    livewire(ListUsers::class)
        ->assertTableActionExists('generateToken');
});

it('has correct globally searchable attributes', function () {
    $globallySearchableAttributes = UserResource::getGloballySearchableAttributes();

    expect($globallySearchableAttributes)->toBe(['email', 'name']);
});

it('validates unique email when creating a user', function () {
    /** @var User */
    $existingUser = User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @phpstan-ignore-next-line */
    livewire(CreateUser::class)
        ->fillForm([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ])
        ->call('create')
        ->assertHasFormErrors(['email' => 'unique']);
});

it('allows same email when editing a user', function () {
    /** @var User */
    $user = User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    /** @phpstan-ignore-next-line */
    livewire(EditUser::class, [
        'record' => $user->id,
    ])
        ->fillForm([
            'name' => 'Updated Name',
            'email' => '<EMAIL>', // Same email
            'password' => 'newpassword123',
        ])
        ->call('save')
        ->assertHasNoFormErrors();
});
