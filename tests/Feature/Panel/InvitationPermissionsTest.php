<?php

use App\Filament\Resources\InvitationResource\Pages\ListInvitations;
use App\Models\Admin;
use App\Models\Invitation;
use App\Models\User;

use function Pest\Laravel\actingAs;
use function Pest\Livewire\livewire;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

it('prevents non-admin users from accessing invitation management', function () {
    /** @var User */
    $user = User::factory()->create();

    actingAs($user, 'web');

    Invitation::factory()->count(3)->create();

    livewire(ListInvitations::class)
        ->assertForbidden();
});

it('allows admin users to access invitation management', function () {
    /** @var Admin */
    $admin = Admin::factory()->create();
    actingAs($admin);

    Invitation::factory()->count(3)->create();

    livewire(ListInvitations::class)
        ->assertSuccessful();
});

it('prevents non-admin users from approving invitations', function () {
    /** @var User */
    $user = User::factory()->create();
    actingAs($user, 'web');

    /** @var Invitation */
    $invitation = Invitation::factory()->pending()->create();

    livewire(ListInvitations::class)
        ->assertForbidden();
});

it('prevents non-admin users from rejecting invitations', function () {
    /** @var User */
    $user = User::factory()->create();
    actingAs($user, 'web');

    /** @var Invitation */
    $invitation = Invitation::factory()->pending()->create();

    livewire(ListInvitations::class)
        ->assertForbidden();
});
