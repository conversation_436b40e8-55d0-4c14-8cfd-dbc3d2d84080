<?php

use App\Filament\Resources\ReelResource\Pages\CreateReel;
use App\Models\Admin;
use App\Models\Creator;
use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\Reel;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\actingAs;

beforeEach(function () {
    Storage::fake('local');
});

it('allows admin to upload reels', function () {
    /** @var Admin $admin */
    $admin = Admin::factory()->create();
    /** @var Partner $partner */
    $partner = Partner::factory()->create();
    /** @var PartnerLocation $location */
    $location = PartnerLocation::factory()->create(['partner_id' => $partner->id]);

    actingAs($admin);

    $videoFile = UploadedFile::fake()->create('test-video.mp4', 1024);
    $thumbnailFile = UploadedFile::fake()->image('thumbnail.jpg');

    $response = \Pest\Livewire\livewire(CreateReel::class)// @phpstan-ignore-line
        ->fillForm([
            'url' => $videoFile,
            'thumbnail' => $thumbnailFile,
            'caption' => 'Test reel caption',
            'partner_id' => $partner->id,
            'creatable_type' => Admin::class,
            'creatable_id' => $admin->id,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    /** @var Reel $reel */
    $reel = Reel::latest('id')->first();

    expect($reel)
        ->and($reel->caption)
        ->toBe('Test reel caption')
        ->and($reel->creatable_type)
        ->toBe(Admin::class)
        ->and($reel->creatable_id)
        ->toBe($admin->id);

    /** @var Creator $creator */
    $creator = $reel->creator;

    expect($creator->getName())
        ->toBe($admin->name);

    expect($reel->toSearchableArray()['_tags'])->toContain('by_place');
});

it('allows creator to upload reels', function () {
    /** @var Creator $creator */
    $creator = Creator::factory()->create();

    /** @var Partner $partner */
    $partner = Partner::factory()->create();

    /** @var PartnerLocation $location */
    $location = PartnerLocation::factory()->create(['partner_id' => $partner->id]);

    /** @var \App\Models\User $user */
    $user = $creator->user;
    actingAs($user);

    $videoFile = UploadedFile::fake()->create('test-video.mp4', 1024);
    $thumbnailFile = UploadedFile::fake()->image('thumbnail.jpg');

    $response = \Pest\Livewire\livewire(CreateReel::class)// @phpstan-ignore-line
        ->fillForm([
            'url' => $videoFile,
            'thumbnail' => $thumbnailFile,
            'caption' => 'Creator reel caption',
            'partner_id' => $partner->id,
            'creatable_type' => Creator::class,
            'creatable_id' => $creator->id,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    $reel = Reel::latest()->first();

    expect($reel)
        ->and($reel->caption)
        ->toBe('Creator reel caption')
        ->and($reel->creatable_type)
        ->toBe(Creator::class)
        ->and($reel->creatable_id)
        ->toBe($creator->id);

    expect($reel->toSearchableArray()['_tags'])->toContain('by_creator');
    expect($reel->toSearchableArray())->toHaveKey('creator');
});
