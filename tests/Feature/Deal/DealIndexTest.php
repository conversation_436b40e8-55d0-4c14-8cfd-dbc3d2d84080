<?php

use App\Models\DealSlot;
use Carbon\Carbon;
use Illuminate\Support\Collection;

test('deals indexing structure', function () {
    /** @var \App\Models\Deal $deal */
    $deal = \App\Models\Deal::factory()->has(DealSlot::factory()->state(fn (array $attributes) => [
        'from' => '00:00',
        'to' => '02:00',
        'day' => now()->addDay()->format('w'),
    ]))->create();

    /** @var \App\Enums\DealType $dealType */
    $dealType = $deal->deal_type;

    expect($deal->toSearchableArray())->toBe([
        'id' => (string) $deal->id,
        'deal_type' => $dealType->name,
        'title' => $deal->title,
        'slots' => $deal->dealSlots->groupBy('day')
            ->mapWithKeys(function (Collection $slots, $day) {
                return [
                    /** @phpstan-ignore-next-line */
                    strtolower(Carbon::getDays()[$day]) => $slots->map(function (DealSlot $slot) {
                        return [
                            'from' => $slot->from,
                            'to' => $slot->to,
                        ];
                    })->toArray(),
                ];
            })->toArray(),
    ]);

    $tags = \App\Models\Tag::factory()->category()->count(2)->create();

    /** @var \App\Models\Deal $newDeal */
    $newDeal = \App\Models\Deal::factory()->create();
    $newDeal->service_options()->sync($tags->pluck('id'));

    /** @var \App\Enums\DealType $newDealType */
    $newDealType = $newDeal->deal_type;

    $newDeal->refresh();
    expect($newDeal->toSearchableArray())->toBe([
        'id' => (string) $newDeal->id,
        'deal_type' => $newDealType->name,
        'title' => $newDeal->title,
        'slots' => $newDeal->dealSlots->groupBy('day')
            ->mapWithKeys(function (Collection $slots, $day) {
                return [
                    /** @phpstan-ignore-next-line */
                    strtolower(Carbon::getDays()[$day]) => $slots->map(function (DealSlot $slot) {
                        return [
                            'from' => $slot->from,
                            'to' => $slot->to,
                        ];
                    })->toArray(),
                ];
            })->toArray(),
        'service_types' => $tags->pluck('title')->toArray(),
    ]);
});
