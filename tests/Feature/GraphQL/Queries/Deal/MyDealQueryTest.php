<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\UserDeal;
use Laravel\Sanctum\Sanctum;

describe('MyDealQuery', function () {
    test('MyDealQuery', function () {

        /** @var User */
        $user = Sanctum::actingAs(User::factory()->create());

        $graphqlResponse = graphQL(/** @lang GraphQL */ '
            {
                myDeal(id: "1") {
                    data { id }
                }
            }
        ');

        expect($graphqlResponse)->toBeObject();
    });

    test('MyDealsQuery', function () {

        /** @var UserDeal $userDeal */
        $userDeal = UserDeal::factory()->create();

        /** @var User $user */
        $user = $userDeal->user;

        \Pest\Laravel\actingAs($user, 'sanctum');

        $graphqlResponse = graphQL(/** @lang GraphQL */ '
            {
                myDeals(first: 10) {
                    data { id }
                }
            }
        ')->assertJson([
            'data' => [
                'myDeals' => [
                    'data' => [
                        [
                            'id' => $userDeal->deal_id,
                        ],
                    ],
                ],
            ],
        ]);
    });

    test('MyDealsQuery from deal object and expect only related records', function () {

        /** @var UserDeal $userDeal */
        $userDeal = UserDeal::factory()->create();

        /** @var User $user */
        $user = $userDeal->user;

        \Pest\Laravel\actingAs($user, 'sanctum');

        UserDeal::factory()->create([
            'user_id' => $user->id,
        ]);

        $graphqlResponse = graphQL(/** @lang GraphQL */ '
            {
               deals(first: 2) {
                   data {
                       id
                       title
                       myDeals(first: 10) {
                        data { 
                            id 
                        }
                      }   
                   }
               }
            }
        ');

        $graphqlResponse->assertJson([
            'data' => [
                'deals' => [
                    'data' => [
                        [
                            'id' => $userDeal->deal_id,
                            'myDeals' => [
                                'data' => [
                                    [
                                        'id' => $userDeal->id,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $graphqlResponse->assertJsonCount(1, 'data.deals.data.0.myDeals');
    });
});
