<?php

declare(strict_types=1);

use App\Models\Deal;

describe('Deal Queries', function () {
    test('fetch a single deal', function () {
        /** @var Deal */
        $deal = Deal::factory()->create();

        $response = graphQL(/** @lang GraphQL */ '
            query ($id: ID!) {
                deal(id: $id) {
                    id
                    title
                    description
                    deal_type
                    reuse_limit_days
                    partner_place {
                        id
                        name
                    }
                    available_slots {
                        date
                        available_seats
                        slots {
                            from
                            to
                        }
                    }
                }
            }
        ', [
            'id' => $deal->id,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'deal' => [
                    'id',
                    'title',
                    'description',
                    'deal_type',
                    'reuse_limit_days',
                    'partner_place' => [
                        'id',
                        'name',
                    ],
                    'available_slots' => [
                        '*' => [
                            'date',
                            'available_seats',
                            'slots' => [
                                '*' => [
                                    'from',
                                    'to',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    });

    test('fetch all deals with pagination', function () {
        Deal::factory()->count(15)->create();

        $response = graphQL(/** @lang GraphQL */ '
            query ($first: Int!, $page: Int!) {
                deals(first: $first, page: $page) {
                    data {
                        id
                        title
                    }
                    paginatorInfo {
                        currentPage
                        lastPage
                        total
                    }
                }
            }
        ', [
            'first' => 10,
            'page' => 1,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'deals' => [
                    'data' => [
                        '*' => [
                            'id',
                            'title',
                        ],
                    ],
                    'paginatorInfo' => [
                        'currentPage',
                        'lastPage',
                        'total',
                    ],
                ],
            ],
        ]);
    });

    test('fetch deal with invalid ID', function () {
        $response = graphQL(/** @lang GraphQL */ '
            query {
                deal(id: "invalid") {
                    id
                }
            }
        ');

        $response->assertGraphQLErrorMessage('Validation failed for the field [deal].');
    });

    test('fetch deals with invalid pagination parameters', function () {
        $response = graphQL(/** @lang GraphQL */ '
            query {
                deals(first: 0, page: 0) {
                    data {
                        id
                    }
                }
            }
        ');

        // Since @paginate doesn't validate parameters, we expect a successful response
        $response->assertJsonStructure([
            'data' => [
                'deals' => [
                    'data' => [
                        '*' => [
                            'id',
                        ],
                    ],
                ],
            ],
        ]);
    });
});
