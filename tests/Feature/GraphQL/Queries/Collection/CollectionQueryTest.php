<?php

use App\Models\Collection;
use App\Models\User;

it('returns only the authenticated user\'s collections for collections query', function () {
    /** @var \App\Models\User $user */
    $user = User::factory()->create();

    /** @var \App\Models\User $otherUser */
    $otherUser = User::factory()->create();

    /** @var \Illuminate\Support\Collection<Collection> $collections */
    $collections = Collection::factory()->count(3)->for($user)->create();

    Collection::factory()->count(2)->for($otherUser)->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        query {
            collections {
                id
                title
            }
        }
    ')->assertJson([
        'data' => [
            'collections' => [
                [
                    'id' => (string) $collections[0]->id,
                ],
            ],
        ],
    ]);

    expect($response->json('data.collections'))->toHaveCount(3);
    foreach ($collections as $collection) {
        $response->assertJsonFragment([
            'id' => (string) $collection->id,
            'title' => $collection->title,
        ]);
    }
});

it('returns a single collection for collection query if owned by user', function () {
    /** @var User $user */
    $user = User::factory()->create();

    /** @var Collection $collection */
    $collection = Collection::factory()->for($user)->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        query($id: ID!) {
            collection(id: $id) {
                id
                title
            }
        }
    ', ['id' => $collection->id]);

    $response->assertJson([
        'data' => [
            'collection' => [
                'id' => (string) $collection->id,
                'title' => $collection->title,
            ],
        ],
    ]);
});

it('returns null for collection query if not owned by user', function () {
    /** @var User $user */
    $user = User::factory()->create();

    /** @var User $otherUser */
    $otherUser = User::factory()->create();

    /** @var Collection $collection */
    $collection = Collection::factory()->for($otherUser)->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        query($id: ID!) {
            collection(id: $id) {
                id
                title
            }
        }
    ', ['id' => $collection->id]);

    $response->assertJson([
        'data' => [
            'collection' => null,
        ],
    ]);
});

it('returns null for collection query if collection does not exist', function () {
    $user = User::factory()->create();
    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        query($id: ID!) {
            collection(id: $id) {
                id
                title
            }
        }
    ', ['id' => 999999]);

    $response->assertJson([
        'data' => [
            'collection' => null,
        ],
    ]);
});
