<?php

declare(strict_types=1);

use App\Models\Creator;
use App\Models\Partner;
use App\Models\PartnerLocation;
use App\Models\Reel;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Creator Places Query', function () {
    test('it returns places where creator has posted reels', function () {
        /** @var Creator $creator */
        $creator = Creator::factory()->create();

        /** @var Partner $partner */
        $partner = Partner::factory()->create();

        /** @var PartnerLocation $place1 */
        $place1 = PartnerLocation::factory()->create(['partner_id' => $partner->id]);
        /** @var PartnerLocation $place2 */
        $place2 = PartnerLocation::factory()->create(['partner_id' => $partner->id]);
        /** @var PartnerLocation $unrelatedPlace */
        $unrelatedPlace = PartnerLocation::factory()->create(['partner_id' => $partner->id]);

        // Create reels by creator for place1 and place2
        /** @var Reel $reel1 */
        $reel1 = Reel::factory()->create([
            'partner_id' => $partner->id,
            'creatable_type' => Creator::class,
            'creatable_id' => $creator->id,
        ]);
        /** @var Reel $reel2 */
        $reel2 = Reel::factory()->create([
            'partner_id' => $partner->id,
            'creatable_type' => Creator::class,
            'creatable_id' => $creator->id,
        ]);

        // Attach reels to places
        $reel1->locations()->attach($place1->id);
        $reel2->locations()->attach($place2->id);

        $response = graphQL(/** @lang GraphQL */ '
            query ($id: ID!, $first: Int!) {
                creator(id: $id) {
                    id
                    places(first: $first) {
                        data {
                            id
                            name
                        }
                        paginatorInfo {
                            total
                            currentPage
                            hasMorePages
                        }
                    }
                }
            }
        ', [
            'id' => $creator->id,
            'first' => 10,
        ]);

        $response->assertJson([
            'data' => [
                'creator' => [
                    'id' => (string) $creator->id,
                    'places' => [
                        'data' => [
                            [
                                'id' => (string) $place1->id,
                                'name' => $place1->name,
                            ],
                            [
                                'id' => (string) $place2->id,
                                'name' => $place2->name,
                            ],
                        ],
                        'paginatorInfo' => [
                            'total' => 2,
                            'currentPage' => 1,
                            'hasMorePages' => false,
                        ],
                    ],
                ],
            ],
        ]);
    });

    test('it returns empty places array when creator has no reels', function () {
        /** @var Creator $creator */
        $creator = Creator::factory()->create();

        $response = graphQL(/** @lang GraphQL */ '
            query ($id: ID!, $first: Int!) {
                creator(id: $id) {
                    id
                    places(first: $first) {
                        data {
                            id
                            name
                        }
                        paginatorInfo {
                            total
                        }
                    }
                }
            }
        ', [
            'id' => $creator->id,
            'first' => 10,
        ]);

        $response->assertJson([
            'data' => [
                'creator' => [
                    'id' => (string) $creator->id,
                    'places' => [
                        'data' => [],
                        'paginatorInfo' => [
                            'total' => 0,
                        ],
                    ],
                ],
            ],
        ]);
    });

    test('it returns distinct places when creator has multiple reels at same place', function () {
        /** @var Creator $creator */
        $creator = Creator::factory()->create();
        /** @var Partner $partner */
        $partner = Partner::factory()->create();
        /** @var PartnerLocation $place */
        $place = PartnerLocation::factory()->create(['partner_id' => $partner->id]);

        // Create multiple reels by creator for the same place
        /** @var Reel $reel1 */
        $reel1 = Reel::factory()->create([
            'partner_id' => $partner->id,
            'creatable_type' => Creator::class,
            'creatable_id' => $creator->id,
        ]);
        /** @var Reel $reel2 */
        $reel2 = Reel::factory()->create([
            'partner_id' => $partner->id,
            'creatable_type' => Creator::class,
            'creatable_id' => $creator->id,
        ]);

        // Attach both reels to the same place
        $reel1->locations()->attach($place->id);
        $reel2->locations()->attach($place->id);

        $response = graphQL(/** @lang GraphQL */ '
            query ($id: ID!, $first: Int!) {
                creator(id: $id) {
                    id
                    places(first: $first) {
                        data {
                            id
                            name
                        }
                        paginatorInfo {
                            total
                        }
                    }
                }
            }
        ', [
            'id' => $creator->id,
            'first' => 10,
        ]);

        $response->assertJson([
            'data' => [
                'creator' => [
                    'id' => (string) $creator->id,
                    'places' => [
                        'data' => [
                            [
                                'id' => (string) $place->id,
                                'name' => $place->name,
                            ],
                        ],
                        'paginatorInfo' => [
                            'total' => 1,
                        ],
                    ],
                ],
            ],
        ]);
    });
});
