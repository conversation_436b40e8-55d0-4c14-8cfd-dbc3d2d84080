<?php

declare(strict_types=1);

use App\Models\Creator;
use App\Models\User;
use Illuminate\Testing\TestResponse;

describe('Creator Is Followed Query', function () {
    test('it returns false when user is not authenticated', function () {
        // Create a creator
        /** @var Creator $creator */
        $creator = Creator::factory()->create();

        // Query the is_followed field without authentication
        /** @var TestResponse $response */
        $response = graphQL(/** @lang GraphQL */ '
            query {
                creator(id: '.$creator->id.') {
                    id
                    is_followed
                }
            }
        ');

        // Assert response structure
        $response->assertJsonStructure([
            'data' => [
                'creator' => [
                    'id',
                    'is_followed',
                ],
            ],
        ]);

        // Assert is_followed is false when not authenticated
        $response->assertJson([
            'data' => [
                'creator' => [
                    'is_followed' => false,
                ],
            ],
        ]);
    });

    test('it returns true when user follows the creator', function () {
        // Create user and creator
        /** @var User $user */
        $user = User::factory()->create();
        /** @var Creator $creator */
        $creator = Creator::factory()->create();

        // Set up following relationship
        $user->following_creators()->attach($creator->id);

        // Authenticate as this user
        \Pest\Laravel\actingAs($user, 'sanctum');

        // Query the is_followed field
        /** @var TestResponse $response */
        $response = graphQL(/** @lang GraphQL */ '
            query {
                creator(id: '.$creator->id.') {
                    id
                    is_followed
                }
            }
        ');

        // Assert response structure and is_followed is true when following
        $response->assertJson([
            'data' => [
                'creator' => [
                    'id' => (string) $creator->id,
                    'is_followed' => true,
                ],
            ],
        ]);
    });

    test('it returns false when user does not follow the creator', function () {
        // Create user and two creators
        /** @var User $user */
        $user = User::factory()->create();
        /** @var Creator $followedCreator */
        $followedCreator = Creator::factory()->create();
        /** @var Creator $unfollowedCreator */
        $unfollowedCreator = Creator::factory()->create();

        // Set up following relationship with only one creator
        $user->following_creators()->attach($followedCreator->id);

        // Authenticate as this user
        \Pest\Laravel\actingAs($user, 'sanctum');

        // Query the is_followed field for the unfollowed creator
        /** @var TestResponse $response */
        $response = graphQL(/** @lang GraphQL */ '
            query {
                creator(id: '.$unfollowedCreator->id.') {
                    id
                    is_followed
                }
            }
        ');

        // Assert is_followed is false when not following
        $response->assertJson([
            'data' => [
                'creator' => [
                    'id' => (string) $unfollowedCreator->id,
                    'is_followed' => false,
                ],
            ],
        ]);
    });
});
