<?php

it('it get all creators', function () {
    \App\Models\Creator::truncate();

    $creators = \App\Models\Creator::factory(3)->create();

    graphQL(/** @lang GraphQL */ '
        {
            creators(first: 10) {
                data {
                    id
                    name
                    bio
                    user {
                        name
                    }
                }
            }
        }
    ', [

    ])->assertJsonCount(3, 'data.creators.data');
});

it('it gets creator by id', function () {
    \App\Models\Creator::truncate();

    /** @var \App\Models\Creator $creator */
    $creator = \App\Models\Creator::factory()->create();

    /** @var \App\Models\User $creatorUser */
    $creatorUser = $creator->user;

    graphQL(/** @lang GraphQL */ '
        query ($id: ID!) {
            creator(id: $id) {
                id
                name
                bio
                user {
                  name
                }
            }
        }
    ', [
        'id' => $creator->id,
    ])->assertJson([
        'data' => [
            'creator' => [
                'id' => (string) $creator->id,
                'name' => $creator->name,
                'bio' => $creator->bio,
                'user' => [
                    'name' => $creatorUser->name,
                ],
            ],
        ],
    ]);
});
