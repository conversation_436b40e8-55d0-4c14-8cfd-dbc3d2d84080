<?php

declare(strict_types=1);

use App\Models\User;
use Laravel\Sanctum\Sanctum;

describe('MeQuery', function () {

    test('Unauthenticated', function () {

        graphQL(/** @lang GraphQL */ '{
            me { email }
        }')->assertGraphQLErrorMessage('Unauthenticated.');
    });

    test('Authenticated', function () {

        /** @var User */
        $user = Sanctum::actingAs(User::factory()->create());

        graphQL(/** @lang GraphQL */ '
        {
            me {
                email
            }
        }')->assertJson([
            'data' => [
                'me' => [
                    'email' => $user->email,
                ],
            ],
        ]);
    });

    test('user can query his following creators', function () {
        /** @var \App\Models\User $user */
        $user = \App\Models\User::factory()->create();

        /** @var \App\Models\Creator $creator */
        $creator = \App\Models\Creator::factory()->create();
        /** @var \App\Models\User $creatorUser */
        $creatorUser = $creator->user;

        $otherUsers = \App\Models\User::factory(3)->create();

        $user->following_creators()->attach($creator->id);

        foreach ($otherUsers as $otherUser) {
            $otherUser->following_creators()->attach($creator->id);
        }

        \Pest\Laravel\actingAs($user, 'sanctum');

        graphQL(/** @lang GraphQL */ '
            {
                me {
                   following_creators(first: 10) {
                       data {
                          id
                          name
                          user {
                            name
                          }
                       }
                   }
                }
            }
        ')->assertJson([
            'data' => [
                'me' => [
                    'following_creators' => [
                        'data' => [
                            [
                                'id' => $creator->id,
                                'name' => $creator->name,
                                'user' => [
                                    'name' => $creatorUser->name,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);

    });

    test('user can query his following places', function () {
        /** @var \App\Models\User $user */
        $user = \App\Models\User::factory()->create();

        /** @var \App\Models\PartnerLocation $place */
        $place = \App\Models\PartnerLocation::factory()->create();

        $otherUsers = \App\Models\User::factory(3)->create();

        $user->following_places()->attach($place->id);

        foreach ($otherUsers as $otherUser) {
            $otherUser->following_places()->attach($place->id);
        }

        \Pest\Laravel\actingAs($user, 'sanctum');

        graphQL(/** @lang GraphQL */ '
            {
                me {
                   following_places(first: 10) {
                       data {
                          id
                          name
                       }
                   }
                }
            }
        ')->assertJson([
            'data' => [
                'me' => [
                    'following_places' => [
                        'data' => [
                            [
                                'id' => $place->id,
                                'name' => $place->name,
                            ],
                        ],
                    ],
                ],
            ],
        ]);

    });

    test('user can query his likes', function () {
        /** @var \App\Models\User $user */
        $user = \App\Models\User::factory()->create();

        /** @var \App\Models\PartnerLocation $place */
        $place = \App\Models\PartnerLocation::factory()->create();

        /** @var \App\Models\Like $like */
        $like = \App\Models\Like::factory()->create([
            'user_id' => $user->id,
            'likeable_id' => $place->id,
            'likeable_type' => $place->getMorphClass(),
        ]);

        $like2 = \App\Models\Like::factory()->create([
            'user_id' => User::factory()->create()->id,
            'likeable_id' => $place->id,
            'likeable_type' => $place->getMorphClass(),
        ]);

        \Pest\Laravel\actingAs($user, 'sanctum');

        graphQL(/** @lang GraphQL */ '
            query {
            likes(first: 10) {
                data {
                  id
                  user {
                    id
                  }
                  likeable {
                      ... on PartnerPlace {
                        namePlace: name
                        __typename
                      }
                      ... on Creator {
                        nameCreator: name
                        __typename
                        user {
                          id
                        }
                      }
                    }
                }
              }
            }
    ')->assertJson([
            'data' => [
                'likes' => [
                    'data' => [
                        [
                            'id' => $like->id,
                            'user' => [
                                'id' => $user->id,
                            ],
                            'likeable' => [
                                'namePlace' => $place->name,
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    });

    test('user can query their creator profile', function () {
        /** @var \App\Models\User $user */
        $user = \App\Models\User::factory()->create();

        /** @var \App\Models\Creator $creator */
        $creator = \App\Models\Creator::factory()->create([
            'user_id' => $user->id,
        ]);

        \Pest\Laravel\actingAs($user, 'sanctum');

        graphQL(/** @lang GraphQL */ '
            {
                me {
                    id
                    name
                    email
                    creator {
                        id
                        name
                        bio
                    }
                }
            }
        ')->assertJson([
            'data' => [
                'me' => [
                    'id' => (string) $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'creator' => [
                        'id' => (string) $creator->id,
                        'name' => $creator->name,
                        'bio' => $creator->bio,
                    ],
                ],
            ],
        ]);
    });

    test('user without creator profile returns null for creator field', function () {
        /** @var \App\Models\User $user */
        $user = \App\Models\User::factory()->create();

        \Pest\Laravel\actingAs($user, 'sanctum');

        graphQL(/** @lang GraphQL */ '
            {
                me {
                    id
                    name
                    email
                    creator {
                        id
                        name
                        bio
                    }
                }
            }
        ')->assertJson([
            'data' => [
                'me' => [
                    'id' => (string) $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'creator' => null,
                ],
            ],
        ]);
    });

});
