<?php

declare(strict_types=1);

use App\Models\Like;
use App\Models\PartnerLocation;
use App\Models\Reel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('ReelsMetaStatuses Query', function () {
    test('it returns meta statuses for multiple reels when unauthenticated', function () {
        // Create reels with likes
        /** @var Reel $reel1 */
        $reel1 = Reel::factory()->create();
        /** @var Reel $reel2 */
        $reel2 = Reel::factory()->create();

        // Create users to like the reels
        /** @var User $user1 */
        $user1 = User::factory()->create();
        /** @var User $user2 */
        $user2 = User::factory()->create();

        // Add likes to reels
        Like::create([
            'user_id' => $user1->id,
            'likeable_id' => $reel1->id,
            'likeable_type' => $reel1->getMorphClass(),
        ]);
        Like::create([
            'user_id' => $user2->id,
            'likeable_id' => $reel1->id,
            'likeable_type' => $reel1->getMorphClass(),
        ]);
        Like::create([
            'user_id' => $user1->id,
            'likeable_id' => $reel2->id,
            'likeable_type' => $reel2->getMorphClass(),
        ]);

        // Create a partner location and attach to reels
        /** @var PartnerLocation $location */
        $location = PartnerLocation::factory()->create();
        $reel1->locations()->attach($location->id);
        $reel2->locations()->attach($location->id);

        // Query as unauthenticated user
        $response = graphQL(/** @lang GraphQL */ '
            query ($ids: [ID!]!) {
                reelsMetaStatuses(ids: $ids) {
                    id
                    likes (first: 10){
                        data {
                            id
                        }
                    }
                    is_liked
                    likes_count
                    is_place_followed
                }
            }
        ', [
            'ids' => [$reel1->id, $reel2->id],
        ]);

        $responseData = $response->json('data');

        // Assert response structure
        $response->assertJsonStructure([
            'data' => [
                'reelsMetaStatuses' => [
                    '*' => [
                        'id',
                        'likes' => [
                            'data' => [
                                '*' => ['id'],
                            ],
                        ],
                        'is_liked',
                        'likes_count',
                        'is_place_followed',
                    ],
                ],
            ],
        ]);

        // Assert specific data points for first reel
        $reel1Data = $response->json('data.reelsMetaStatuses.0');
        expect($reel1Data['likes_count'])->toBe(2);
        expect($reel1Data['is_liked'])->toBeFalse();
        expect($reel1Data['is_place_followed'])->toBeFalse();

        // Assert specific data points for second reel
        $reel2Data = $response->json('data.reelsMetaStatuses.1');
        expect($reel2Data['likes_count'])->toBe(1);
        expect($reel2Data['is_liked'])->toBeFalse();
        expect($reel2Data['is_place_followed'])->toBeFalse();
    });

    test('it returns meta statuses for multiple reels when authenticated', function () {
        // Create reels with likes
        /** @var Reel $reel1 */
        $reel1 = Reel::factory()->create();
        /** @var Reel $reel2 */
        $reel2 = Reel::factory()->create();

        // Create users to like the reels
        /** @var User $user1 */
        $user1 = User::factory()->create();
        /** @var User $user2 */
        $user2 = User::factory()->create();

        // Add likes to reels
        Like::create([
            'user_id' => $user1->id,
            'likeable_id' => $reel1->id,
            'likeable_type' => $reel1->getMorphClass(),
        ]);
        Like::create([
            'user_id' => $user2->id,
            'likeable_id' => $reel1->id,
            'likeable_type' => $reel1->getMorphClass(),
        ]);
        Like::create([
            'user_id' => $user1->id,
            'likeable_id' => $reel2->id,
            'likeable_type' => $reel2->getMorphClass(),
        ]);

        // Create a partner location and attach to reels
        /** @var PartnerLocation $location */
        $location = PartnerLocation::factory()->create();
        $reel1->locations()->attach($location->id);
        $reel2->locations()->attach($location->id);

        $user1->following_places()->attach($location->id);

        \Pest\Laravel\actingAs($user1, 'sanctum');

        // Query as authenticated user
        $response = graphQL(/** @lang GraphQL */ '
            query ($ids: [ID!]!) {
                reelsMetaStatuses(ids: $ids) {
                    id
                    likes (first: 10){
                        data {
                            id
                        }
                    }
                    is_liked
                    likes_count
                    is_place_followed
                }
            }
        ', [
            'ids' => [$reel1->id, $reel2->id],
        ]);

        $responseData = $response->json('data.reelsMetaStatuses');

        // Assert response structure
        $response->assertJsonStructure([
            'data' => [
                'reelsMetaStatuses' => [
                    '*' => [
                        'id',
                        'likes' => [
                            'data' => [
                                '*' => ['id'],
                            ],
                        ],
                        'is_liked',
                        'likes_count',
                        'is_place_followed',
                    ],
                ],
            ],
        ]);

        // Assert specific data points for first reel
        $reel1Data = collect($responseData)->firstWhere('id', (string) $reel1->id);
        expect($reel1Data['likes_count'])->toBe(2);
        expect($reel1Data['is_liked'])->toBeTrue();
        expect($reel1Data['is_place_followed'])->toBeTrue();

        // Assert specific data points for second reel
        $reel2Data = collect($responseData)->firstWhere('id', (string) $reel2->id);
        expect($reel2Data['likes_count'])->toBe(1);
        expect($reel2Data['is_liked'])->toBeTrue();
        expect($reel2Data['is_place_followed'])->toBeTrue();
    });

    test('it returns empty array for non-existent reels', function () {
        $response = graphQL(/** @lang GraphQL */ '
            query ($ids: [ID!]!) {
                reelsMetaStatuses(ids: $ids) {
                    id
                }
            }
        ', [
            'ids' => ['999', '888'],
        ]);

        $response->assertJsonPath('data.reelsMetaStatuses', []);
    });
});
