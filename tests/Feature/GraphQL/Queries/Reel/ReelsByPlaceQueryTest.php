<?php

declare(strict_types=1);

use App\Models\PartnerLocation;
use App\Models\Reel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Testing\TestResponse;

describe('ReelsByPlace Query', function () {
    test('it returns reels for a specific place', function () {
        // Create partner locations
        /** @var PartnerLocation $place1 */
        $place1 = PartnerLocation::factory()->create();
        /** @var PartnerLocation $place2 */
        $place2 = PartnerLocation::factory()->create();

        // Create reels
        /** @var Collection<int, Reel> $reelsForPlace1 */
        $reelsForPlace1 = Reel::factory()->count(3)->create();
        /** @var Collection<int, Reel> $reelsForPlace2 */
        $reelsForPlace2 = Reel::factory()->count(2)->create();
        /** @var Reel $reelForBothPlaces */
        $reelForBothPlaces = Reel::factory()->create();

        // Create associations using pivot table
        foreach ($reelsForPlace1 as $reel) {
            $reel->locations()->attach($place1->id);
        }

        foreach ($reelsForPlace2 as $reel) {
            $reel->locations()->attach($place2->id);
        }

        // This reel is associated with both places
        $reelForBothPlaces->locations()->attach([$place1->id, $place2->id]);

        // Query reels for place1
        /** @var TestResponse $response */
        $response = graphQL(/** @lang GraphQL */ '
            query ($placeId: ID!, $first: Int!) {
                reelsByPlace(placeId: $placeId, first: $first) {
                    data {
                        id
                        caption
                        full_url
                    }
                    paginatorInfo {
                        total
                        currentPage
                        hasMorePages
                    }
                }
            }
        ', [
            'placeId' => $place1->id,
            'first' => 10,
        ]);

        // Assert we get the correct number of reels (3 + 1 = 4)
        $response->assertJson([
            'data' => [
                'reelsByPlace' => [
                    'paginatorInfo' => [
                        'total' => 4,
                    ],
                ],
            ],
        ]);

        // Ensure all reels in the response belong to place1
        /** @var \Illuminate\Support\Collection<int, int> $reelIds */
        $reelIds = collect($response->json('data.reelsByPlace.data'))->pluck('id')->map(fn ($id) => (int) $id);
        /** @var \Illuminate\Support\Collection<int, int> $expectedIds */
        $expectedIds = $reelsForPlace1->pluck('id')->merge([$reelForBothPlaces->id]);

        expect($reelIds->sort()->values()->all())->toBe($expectedIds->sort()->values()->all());

        // Now query reels for place2
        /** @var TestResponse $response */
        $response = graphQL(/** @lang GraphQL */ '
            query ($placeId: ID!, $first: Int!) {
                reelsByPlace(placeId: $placeId, first: $first) {
                    data {
                        id
                    }
                    paginatorInfo {
                        total
                    }
                }
            }
        ', [
            'placeId' => $place2->id,
            'first' => 10,
        ]);

        // Assert we get the correct number of reels (2 + 1 = 3)
        $response->assertJson([
            'data' => [
                'reelsByPlace' => [
                    'paginatorInfo' => [
                        'total' => 3,
                    ],
                ],
            ],
        ]);
    });
});
