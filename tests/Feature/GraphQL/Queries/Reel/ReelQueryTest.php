<?php

declare(strict_types=1);

use App\Models\Reel;

describe('Reel Queries', function () {
    test('fetch a single reel', function () {
        /** @var Reel */
        $reel = Reel::factory()->create();

        $response = graphQL(/** @lang GraphQL */ '
            query ($id: ID!) {
                reel(id: $id) {
                    id
                    caption
                    full_url
                    created_at
                    updated_at
                }
            }
        ', [
            'id' => $reel->id,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'reel' => [
                    'id',
                    'caption',
                    'full_url',
                    'created_at',
                    'updated_at',
                ],
            ],
        ]);
    });

    test('fetch all reels with pagination', function () {
        Reel::factory()->count(15)->create();

        $response = graphQL(/** @lang GraphQL */ '
            query ($first: Int!, $page: Int!) {
                reels(first: $first, page: $page) {
                    data {
                        id
                        caption
                        full_url
                    }
                    paginatorInfo {
                        currentPage
                        lastPage
                        total
                    }
                }
            }
        ', [
            'first' => 10,
            'page' => 1,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'reels' => [
                    'data' => [
                        '*' => [
                            'id',
                            'caption',
                            'full_url',
                        ],
                    ],
                    'paginatorInfo' => [
                        'currentPage',
                        'lastPage',
                        'total',
                    ],
                ],
            ],
        ]);
    });

    test('fetch reel with invalid ID', function () {
        $response = graphQL(/** @lang GraphQL */ '
            query {
                reel(id: "invalid") {
                    id
                }
            }
        ');

        // Since @find doesn't validate IDs, we expect a successful response with null data
        $response->assertJsonStructure([
            'data' => [
                'reel',
            ],
        ]);

        // Verify that the reel is null
        $response->assertJsonPath('data.reel', null);
    });

    test('fetch reels with invalid pagination parameters', function () {
        $response = graphQL(/** @lang GraphQL */ '
            query {
                reels(first: 0, page: 0) {
                    data {
                        id
                    }
                }
            }
        ');

        // Since @paginate doesn't validate parameters, we expect a successful response
        $response->assertJsonStructure([
            'data' => [
                'reels' => [
                    'data' => [
                        '*' => [
                            'id',
                        ],
                    ],
                ],
            ],
        ]);
    });
});
