<?php

use App\Enums\InvitationStatus;
use App\Models\Invitation;
use App\Models\User;

beforeEach(function () {
    $this->inviter = User::factory()->create();
    $this->invitee = User::factory()->create();
});

it('allows user to accept valid invitation', function () {
    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $this->inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
    ]);

    $response = $this->actingAs($this->invitee, 'sanctum')->graphQL(/** @lang GraphQL */ '
        mutation ($token: String!, $inviterUserId: ID!) {
            acceptInvitation(token: $token, inviterUserId: $inviterUserId) {
                user {
                    id
                    email
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
        'inviterUserId' => $this->inviter->id,
    ]);

    $response->assertJson([
        'data' => [
            'acceptInvitation' => [
                'user' => [
                    'id' => (string) $this->invitee->id,
                    'email' => $this->invitee->email,
                ],
                'statusCode' => 200,
            ]
        ]
    ]);

    // Verify invitation was updated
    $invitation->refresh();
    expect($invitation->is_used)->toBeTrue()
        ->and($invitation->invitee_user_id)->toBe($this->invitee->id)
        ->and($invitation->email)->toBe($this->invitee->email)
        ->and($invitation->status)->toBe(InvitationStatus::APPROVED);
});

it('rejects invalid invitation token', function () {
    $response = $this->actingAs($this->invitee, 'sanctum')->graphQL(/** @lang GraphQL */ '
        mutation ($token: String!, $inviterUserId: ID!) {
            acceptInvitation(token: $token, inviterUserId: $inviterUserId) {
                user {
                    id
                }
                statusCode
            }
        }
    ', [
        'token' => 'invalid-token',
        'inviterUserId' => $this->inviter->id,
    ]);

    $response->assertGraphQLErrorMessage('Invalid invitation token');
});

it('rejects expired invitation token', function () {
    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $this->inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->subDays(1), // Expired
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
    ]);

    $response = $this->actingAs($this->invitee, 'sanctum')->graphQL(/** @lang GraphQL */ '
        mutation ($token: String!, $inviterUserId: ID!) {
            acceptInvitation(token: $token, inviterUserId: $inviterUserId) {
                user {
                    id
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
        'inviterUserId' => $this->inviter->id,
    ]);

    $response->assertGraphQLError('Invitation link has expired');
});

it('rejects already used invitation', function () {
    $invitation = Invitation::factory()->create([
        'inviter_user_id' => $this->inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => true, // Already used
        'status' => InvitationStatus::APPROVED,
    ]);

    $response = $this->actingAs($this->invitee, 'sanctum')->graphQL(/** @lang GraphQL */ '
        mutation ($token: String!, $inviterUserId: ID!) {
            acceptInvitation(token: $token, inviterUserId: $inviterUserId) {
                user {
                    id
                }
                statusCode
            }
        }
    ', [
        'token' => $invitation->token,
        'inviterUserId' => $this->inviter->id,
    ]);

    $response->assertGraphQLError('Invitation has already been used');
});

it('handles user with existing pending admin invitation', function () {
    // Create existing admin invitation for user
    $adminInvitation = Invitation::factory()->create([
        'email' => $this->invitee->email,
        'status' => InvitationStatus::PENDING,
        'inviter_user_id' => null, // Admin invitation
        'token' => null,
    ]);

    // Create user invitation
    $userInvitation = Invitation::factory()->create([
        'inviter_user_id' => $this->inviter->id,
        'token' => Invitation::generateToken(),
        'token_expires_at' => now()->addDays(7),
        'is_used' => false,
        'status' => InvitationStatus::PENDING,
    ]);

    $response = $this->actingAs($this->invitee, 'sanctum')->graphQL(/** @lang GraphQL */ '
        mutation ($token: String!, $inviterUserId: ID!) {
            acceptInvitation(token: $token, inviterUserId: $inviterUserId) {
                user {
                    id
                    email
                }
                statusCode
            }
        }
    ', [
        'token' => $userInvitation->token,
        'inviterUserId' => $this->inviter->id,
    ]);

    $response->assertJson([
        'data' => [
            'acceptInvitation' => [
                'user' => [
                    'id' => (string) $this->invitee->id,
                ],
                'statusCode' => 200,
            ]
        ]
    ]);

    // Verify admin invitation was updated with referral and approved
    $adminInvitation->refresh();
    expect($adminInvitation->inviter_user_id)->toBe($this->inviter->id)
        ->and($adminInvitation->status)->toBe(InvitationStatus::APPROVED);

    // Verify user invitation was marked as used
    $userInvitation->refresh();
    expect($userInvitation->is_used)->toBeTrue()
        ->and($userInvitation->invitee_user_id)->toBe($this->invitee->id);
});
