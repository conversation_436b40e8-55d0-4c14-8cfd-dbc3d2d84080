<?php

use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

test('user can like and unlike a place', function () {
    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    /** @var \App\Models\PartnerLocation $place */
    $place = \App\Models\PartnerLocation::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!) {
            like(input: {
                type: PARTNER_PLACE
                id: $id
            }) {
                status
                message
                likeable {
                    ... on PartnerPlace {
                      id
                    }
              }
            }
        }
    ', ['id' => $place->id])->assertJson([
        'data' => [
            'like' => [
                'status' => true,
                'message' => 'Liked Successfully',
                'likeable' => [
                    'id' => $place->id,
                ],
            ],
        ],
    ]);

    assertDatabaseHas(\App\Models\Like::class, [
        'user_id' => $user->id,
        'likeable_id' => $place->id,
        'likeable_type' => $place->getMorphClass(),
    ]);

    graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!) {
            like(input: {
                type: PARTNER_PLACE
                id: $id
            }) {
                status
                message
            }
        }
    ', ['id' => $place->id])->assertJson([
        'data' => [
            'like' => [
                'status' => true,
                'message' => 'unLiked Successfully',
            ],
        ],
    ]);

    assertDatabaseMissing(\App\Models\Like::class, [
        'user_id' => $user->id,
        'likeable_id' => $place->id,
        'likeable_type' => $place->getMorphClass(),
    ]);
});

test('user can not like an invalid type', function () {
    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    /** @var \App\Models\PartnerLocation $place */
    $place = \App\Models\PartnerLocation::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!) {
            like(input: {
                type: INVALID
                id: $id
            }) {
                status
                message
            }
        }
    ', ['id' => $place->id])->assertGraphQLErrorMessage('Value "INVALID" does not exist in "LikableType" enum.');
});
