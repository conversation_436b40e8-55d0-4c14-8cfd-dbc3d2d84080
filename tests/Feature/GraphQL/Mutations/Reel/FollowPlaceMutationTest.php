<?php

test('user can follow a place', function () {
    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    /** @var \App\Models\PartnerLocation $place */
    $place = \App\Models\PartnerLocation::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!) {
              followPlace(input: {
                id: $id
              }) {
                status
                message
              }
            }
    ', ['id' => $place->id])->assertJson([
        'data' => [
            'followPlace' => [
                'status' => true,
                'message' => 'Place followed',
            ],
        ],
    ]);
});

test('user can unfollow a creator', function () {
    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    /** @var \App\Models\PartnerLocation $place */
    $place = \App\Models\PartnerLocation::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!) {
              followPlace(input: {
                id: $id
              }) {
                status
                message
              }
            }
    ', ['id' => $place->id])->assertJson([
        'data' => [
            'followPlace' => [
                'status' => true,
                'message' => 'Place followed',
            ],
        ],
    ]);

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!) {
              followPlace(input: {
                id: $id
              }) {
                status
                message
              }
            }
    ', ['id' => $place->id])->assertJson([
        'data' => [
            'followPlace' => [
                'status' => true,
                'message' => 'Place unfollowed',
            ],
        ],
    ]);
});
