<?php

test('user can follow a creator', function () {
    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    /** @var \App\Models\Creator $creator */
    $creator = \App\Models\Creator::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!) {
              followCreator(input: {
                id: $id
              }) {
                status
                message
              }
            }
    ', ['id' => $creator->id])->assertJson([
        'data' => [
            'followCreator' => [
                'status' => true,
                'message' => 'Creator followed',
            ],
        ],
    ]);
});

test('user can unfollow a creator', function () {
    /** @var \App\Models\User $user */
    $user = \App\Models\User::factory()->create();

    /** @var \App\Models\Creator $creator */
    $creator = \App\Models\Creator::factory()->create();

    \Pest\Laravel\actingAs($user, 'sanctum');

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!) {
              followCreator(input: {
                id: $id
              }) {
                status
                message
              }
            }
    ', ['id' => $creator->id])->assertJson([
        'data' => [
            'followCreator' => [
                'status' => true,
                'message' => 'Creator followed',
            ],
        ],
    ]);

    graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!) {
              followCreator(input: {
                id: $id
              }) {
                status
                message
              }
            }
    ', ['id' => $creator->id])->assertJson([
        'data' => [
            'followCreator' => [
                'status' => true,
                'message' => 'Creator unfollowed',
            ],
        ],
    ]);
});
