<?php

use App\Models\Collection;
use App\Models\User;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertSoftDeleted;

it('allows a user to create a collection', function () {
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    $title = 'My New Test Collection';
    $description = 'A description for the new collection.';

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: CreateCollectionInput!) {
            createCollection(input: $input) {
                id
                title
                description
                user { id }
            }
        }
    ', [
        'input' => [
            'title' => $title,
            'description' => $description,
        ],
    ]);

    $response->assertJson([
        'data' => [
            'createCollection' => [
                'title' => $title,
                'description' => $description,
                'user' => ['id' => (string) $user->id],
            ],
        ],
    ]);

    $collectionId = $response->json('data.createCollection.id');

    assertDatabaseHas('collections', [
        'id' => $collectionId,
        'title' => $title,
        'description' => $description,
        'user_id' => $user->id,
    ]);
});

it('allows a user to update their collection', function () {
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Collection $collection */
    $collection = Collection::factory()->for($user)->create([
        'title' => 'Old Title',
        'description' => 'Old Description',
    ]);

    $newTitle = 'Updated Collection Title';
    $newDescription = 'Updated description for the collection.';

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: EditCollectionInput!) {     # Changed from UpdateCollectionInput
            editCollection(input: $input) {         # Changed from updateCollection
                id
                title
                description
                user { id }
            }
        }
    ', [
        'input' => [
            'id' => $collection->id,
            'title' => $newTitle,
            'description' => $newDescription,
        ],
    ]);

    $response->assertJson([
        'data' => [
            'editCollection' => [                     // Changed from updateCollection
                'id' => (string) $collection->id,
                'title' => $newTitle,
                'description' => $newDescription,
                'user' => ['id' => (string) $user->id],
            ],
        ],
    ]);

    assertDatabaseHas('collections', [
        'id' => $collection->id,
        'title' => $newTitle,
        'description' => $newDescription,
        'user_id' => $user->id,
    ]);
});

it('allows a user to delete their collection', function () {
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Collection $collection */
    $collection = Collection::factory()->for($user)->create();
    $collectionId = $collection->id;

    $response = graphQL(/** @lang GraphQL */ '
        mutation($id: ID!) {
            deleteCollection(id: $id)
        }
    ', [
        'id' => $collectionId,
    ]);

    $response->assertJson([
        'data' => [
            'deleteCollection' => true,
        ],
    ]);

    assertSoftDeleted('collections', [
        'id' => $collectionId,
    ]);
});

// Test for trying to update another user's collection (should fail)
it('prevents a user from updating another user\'s collection', function () {
    /** @var User $userOne */
    $userOne = User::factory()->create();
    /** @var User $userTwo */
    $userTwo = User::factory()->create();

    actingAs($userOne, 'sanctum'); // Authenticated as userOne

    /** @var Collection $collectionOfUserTwo */
    $collectionOfUserTwo = Collection::factory()->for($userTwo)->create([
        'title' => 'User Two\'s Collection',
    ]);

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: EditCollectionInput!) {     # Changed from UpdateCollectionInput
            editCollection(input: $input) {         # Changed from updateCollection
                id
                title
            }
        }
    ', [
        'input' => [
            'id' => $collectionOfUserTwo->id,
            'title' => 'Attempted Update Title',
        ],
    ]);

    // Expecting an authorization error or for the data to be null
    $response->assertJsonPath('data.editCollection', null); // Changed from updateCollection
    // Or check for a specific error message if your GraphQL setup returns one
    // $response->assertJsonFragment(['message' => 'This action is unauthorized.']);

    assertDatabaseHas('collections', [
        'id' => $collectionOfUserTwo->id,
        'title' => 'User Two\'s Collection', // Title should remain unchanged
    ]);
});

// Test for trying to delete another user's collection (should fail)
it('prevents a user from deleting another user\'s collection', function () {
    /** @var User $userOne */
    $userOne = User::factory()->create();
    /** @var User $userTwo */
    $userTwo = User::factory()->create();

    actingAs($userOne, 'sanctum'); // Authenticated as userOne

    /** @var Collection $collectionOfUserTwo */
    $collectionOfUserTwo = Collection::factory()->for($userTwo)->create();

    $response = graphQL(/** @lang GraphQL */ '
        mutation($id: ID!) {
            deleteCollection(id: $id) {
                id
            }
        }
    ', [
        'id' => $collectionOfUserTwo->id,
    ]);

    // Expecting an authorization error or for the data to be null
    $response->assertJsonPath('data.deleteCollection', null);
    // Or check for a specific error message
    // $response->assertJsonFragment(['message' => 'This action is unauthorized.']);

    assertDatabaseHas('collections', [ // Collection should still exist
        'id' => $collectionOfUserTwo->id,
    ]);
});
