<?php

use App\Models\Collection;
use App\Models\Creator;
use App\Models\PartnerLocation;
use App\Models\Reel;
use App\Models\User;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing; // Use the actingAs helper directly

it('allows a user to add a Reel to a collection', function () {
    /** @var User $user */
    $user = User::factory()->create();

    /** @var Collection $collection */
    $collection = Collection::factory()->for($user)->create();

    /** @var Reel $reel */
    $reel = Reel::factory()->create();

    actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            'collection_ids' => [$collection->id],
            'collectable_type' => 'REEL',
            'collectable_id' => $reel->id,
        ],
    ]);

    $response->assertJsonFragment([
        'id' => (string) $collection->id,
    ]);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_type', Reel::class);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_id', (string) $reel->id);

    assertDatabaseHas('collection_items', [
        'collection_id' => $collection->id,
        'collectable_type' => Reel::class,
        'collectable_id' => $reel->id,
    ]);
});

it('allows a user to add a Creator to a collection', function () {
    /** @var User $user */
    $user = User::factory()->create();
    /** @var Collection $collection */
    $collection = Collection::factory()->for($user)->create();
    /** @var Creator $creator */
    $creator = Creator::factory()->create();

    actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            'collection_ids' => [$collection->id],
            'collectable_type' => 'CREATOR',
            'collectable_id' => $creator->id,
        ],
    ]);

    $response->assertJsonFragment([
        'id' => (string) $collection->id,
    ]);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_type', Creator::class);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_id', (string) $creator->id);

    assertDatabaseHas('collection_items', [
        'collection_id' => $collection->id,
        'collectable_type' => Creator::class,
        'collectable_id' => $creator->id,
    ]);
});

it('allows a user to add a PartnerLocation to a collection (default type)', function () {
    /** @var User $user */
    $user = User::factory()->create();

    /** @var Collection $collection */
    $collection = Collection::factory()->for($user)->create();

    /** @var PartnerLocation $place */
    $place = PartnerLocation::factory()->create();

    actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            'collection_ids' => [$collection->id],
            'collectable_id' => $place->id,
            // collectable_type defaults to PARTNER_LOCATION
        ],
    ]);

    $response->assertJsonFragment([
        'id' => (string) $collection->id,
    ]);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_type', PartnerLocation::class);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_id', (string) $place->id);

    assertDatabaseHas('collection_items', [
        'collection_id' => $collection->id,
        'collectable_type' => PartnerLocation::class,
        'collectable_id' => $place->id,
    ]);
});

it('creates a new collection and adds item if collection_ids is missing and user has no collections', function () {
    /** @var User $user */
    $user = User::factory()->create();
    /** @var PartnerLocation $place */
    $place = PartnerLocation::factory()->create();
    actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            // collection_ids is omitted, or could be an empty array []
            'collectable_id' => $place->id,
        ],
    ]);

    $response->assertJsonCount(1, 'data.addItemToCollection');
    $collectionId = $response->json('data.addItemToCollection.0.id');

    /** @var Collection $collection */
    $collection = Collection::find($collectionId);
    expect($collection)->not->toBeNull();
    expect($collection->title)->toBe('My Collection');
    expect($collection->user_id)->toBe($user->id);

    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_type', PartnerLocation::class);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_id', (string) $place->id);

    assertDatabaseHas('collections', [
        'id' => $collectionId,
        'user_id' => $user->id,
        'title' => 'My Collection',
    ]);
    assertDatabaseHas('collection_items', [
        'collection_id' => $collectionId,
        'collectable_type' => PartnerLocation::class,
        'collectable_id' => $place->id,
    ]);
});

it('creates a new collection and adds item if collection_ids contains an invalid ID', function () {
    /** @var User $user */
    $user = User::factory()->create();

    /** @var PartnerLocation $place */
    $place = PartnerLocation::factory()->create();

    /** @var Collection $existingCollection */
    $existingCollection = Collection::factory()->for($user)->create();

    actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            'collection_ids' => [999999], // Invalid ID
            'collectable_id' => $place->id,
        ],
    ]);

    $response->assertJsonCount(1, 'data.addItemToCollection');
    $newCollectionId = $response->json('data.addItemToCollection.0.id');

    /** @var Collection $newCollection */
    $newCollection = Collection::find($newCollectionId);
    expect($newCollection)->not->toBeNull();
    expect($newCollection->title)->toBe('My Collection');
    expect($newCollection->user_id)->toBe($user->id);
    expect($newCollection->id)->not->toBe($existingCollection->id);

    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_type', PartnerLocation::class);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_id', (string) $place->id);

    assertDatabaseHas('collections', [
        'id' => $newCollectionId,
        'user_id' => $user->id,
        'title' => 'My Collection',
    ]);
    assertDatabaseHas('collection_items', [
        'collection_id' => $newCollectionId,
        'collectable_type' => PartnerLocation::class,
        'collectable_id' => $place->id,
    ]);

    assertDatabaseMissing('collection_items', [
        'collection_id' => $existingCollection->id,
        'collectable_id' => $place->id,
    ]);
});

it('adds an item to multiple existing collections', function () {
    /** @var User $user */
    $user = User::factory()->create();

    /** @var Collection $collection1 */
    $collection1 = Collection::factory()->for($user)->create(['title' => 'Collection One']);
    /** @var Collection $collection2 */
    $collection2 = Collection::factory()->for($user)->create(['title' => 'Collection Two']);

    /** @var Reel $reel */
    $reel = Reel::factory()->create();

    actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            'collection_ids' => [$collection1->id, $collection2->id],
            'collectable_type' => 'REEL',
            'collectable_id' => $reel->id,
        ],
    ]);

    $response->assertJsonCount(2, 'data.addItemToCollection');

    $response->assertJsonFragment([
        'id' => (string) $collection1->id,
        'title' => 'Collection One',
    ]);
    // Check item in the first collection returned (order might vary, so we check by properties)
    $item1 = collect($response->json('data.addItemToCollection.0.items'))->first();
    expect($item1['collectable_id'])->toBe((string) $reel->id);

    $response->assertJsonFragment([
        'id' => (string) $collection2->id,
        'title' => 'Collection Two',
    ]);
    // Check item in the second collection returned
    $item2 = collect($response->json('data.addItemToCollection.1.items'))->first();
    expect($item2['collectable_id'])->toBe((string) $reel->id);

    assertDatabaseHas('collection_items', [
        'collection_id' => $collection1->id,
        'collectable_type' => Reel::class,
        'collectable_id' => $reel->id,
    ]);
    assertDatabaseHas('collection_items', [
        'collection_id' => $collection2->id,
        'collectable_type' => Reel::class,
        'collectable_id' => $reel->id,
    ]);
});

it('adds an item to one existing collection and creates one new collection if one id is invalid', function () {
    /** @var User $user */
    $user = User::factory()->create();

    /** @var Collection $existingCollection */
    $existingCollection = Collection::factory()->for($user)->create(['title' => 'Existing Collection']);

    /** @var PartnerLocation $place */
    $place = PartnerLocation::factory()->create();

    actingAs($user, 'sanctum');

    $invalidCollectionId = 999888;

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            'collection_ids' => [$existingCollection->id, $invalidCollectionId],
            'collectable_id' => $place->id,
            'collectable_type' => 'PARTNER_LOCATION',
        ],
    ]);

    $response->assertJsonCount(2, 'data.addItemToCollection');

    $returnedCollections = collect($response->json('data.addItemToCollection'));

    $returnedExistingCollection = $returnedCollections->firstWhere('id', (string) $existingCollection->id);
    expect($returnedExistingCollection)->not->toBeNull();
    expect($returnedExistingCollection['title'])->toBe('Existing Collection');
    expect($returnedExistingCollection['items'][0]['collectable_id'])->toBe((string) $place->id);
    expect($returnedExistingCollection['items'][0]['collectable_type'])->toBe(PartnerLocation::class);

    $returnedNewCollection = $returnedCollections->firstWhere('id', '!=', (string) $existingCollection->id);
    expect($returnedNewCollection)->not->toBeNull();
    expect($returnedNewCollection['title'])->toBe('My Collection');
    expect($returnedNewCollection['items'][0]['collectable_id'])->toBe((string) $place->id);
    expect($returnedNewCollection['items'][0]['collectable_type'])->toBe(PartnerLocation::class);
    $newCollectionId = $returnedNewCollection['id'];

    assertDatabaseHas('collection_items', [
        'collection_id' => $existingCollection->id,
        'collectable_type' => PartnerLocation::class,
        'collectable_id' => $place->id,
    ]);
    assertDatabaseHas('collections', [
        'id' => $newCollectionId,
        'user_id' => $user->id,
        'title' => 'My Collection',
    ]);
    assertDatabaseHas('collection_items', [
        'collection_id' => $newCollectionId,
        'collectable_type' => PartnerLocation::class,
        'collectable_id' => $place->id,
    ]);
});
