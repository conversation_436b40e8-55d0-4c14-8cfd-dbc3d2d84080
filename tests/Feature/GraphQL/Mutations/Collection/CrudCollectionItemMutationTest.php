<?php

use App\Models\Collection;
// Kept as it's used
use App\Models\Reel;
use App\Models\User;
// Will rely on global setup or TestCase for this
use Illuminate\Support\Facades\Notification;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
// Kept as it might be used or useful
use function Pest\Laravel\assertSoftDeleted;

it('addItemToCollection mutation adds item to existing collection', function () {
    Notification::fake();
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Collection $collection */
    $collection = Collection::factory()->for($user)->create();
    /** @var Reel $reel */
    $reel = Reel::factory()->create();

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            'collection_ids' => [(string) $collection->id],
            'collectable_type' => 'REEL',
            'collectable_id' => (string) $reel->id,
        ],
    ]);

    $response->assertJsonFragment([
        'id' => (string) $collection->id,
    ]);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_type', Reel::class);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_id', (string) $reel->id);

    assertDatabaseHas('collection_items', [
        'collection_id' => $collection->id,
        'collectable_type' => Reel::class,
        'collectable_id' => $reel->id,
        'deleted_at' => null,
    ]);
});

it('addItemToCollection mutation creates new collection if no collection_ids provided', function () {
    Notification::fake();
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Reel $reel */
    $reel = Reel::factory()->create();

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            // collection_ids is omitted
            'collectable_type' => 'REEL',
            'collectable_id' => (string) $reel->id,
        ],
    ]);

    $response->assertJsonCount(1, 'data.addItemToCollection');
    $newCollectionId = $response->json('data.addItemToCollection.0.id');

    /** @var Collection $newCollection */
    $newCollection = Collection::find($newCollectionId);
    expect($newCollection)->not->toBeNull();
    expect($newCollection->title)->toBe('My Collection'); // Default title
    expect($newCollection->user_id)->toBe($user->id);

    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_type', Reel::class);
    $response->assertJsonPath('data.addItemToCollection.0.items.0.collectable_id', (string) $reel->id);

    assertDatabaseHas('collections', [
        'id' => $newCollectionId,
        'user_id' => $user->id,
        'title' => 'My Collection',
    ]);
    assertDatabaseHas('collection_items', [
        'collection_id' => $newCollectionId,
        'collectable_type' => Reel::class,
        'collectable_id' => $reel->id,
        'deleted_at' => null,
    ]);
});

it('addItemToCollection mutation adds item to multiple collections', function () {
    Notification::fake();
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Collection $collection1 */
    $collection1 = Collection::factory()->for($user)->create(['title' => 'Coll One']);
    /** @var Collection $collection2 */
    $collection2 = Collection::factory()->for($user)->create(['title' => 'Coll Two']);
    /** @var Reel $reel */
    $reel = Reel::factory()->create();

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: AddItemToCollectionInput!) {
            addItemToCollection(input: $input) {
                id
                title
                items {
                  collectable_type
                  collectable_id
                }
            }
        }
    ', [
        'input' => [
            'collection_ids' => [(string) $collection1->id, (string) $collection2->id],
            'collectable_type' => 'REEL',
            'collectable_id' => (string) $reel->id,
        ],
    ]);

    $response->assertJsonCount(2, 'data.addItemToCollection');
    $response->assertJsonFragment(['id' => (string) $collection1->id, 'title' => 'Coll One']);
    $response->assertJsonFragment(['id' => (string) $collection2->id, 'title' => 'Coll Two']);

    assertDatabaseHas('collection_items', [
        'collection_id' => $collection1->id,
        'collectable_type' => Reel::class,
        'collectable_id' => $reel->id,
        'deleted_at' => null,
    ]);
    assertDatabaseHas('collection_items', [
        'collection_id' => $collection2->id,
        'collectable_type' => Reel::class,
        'collectable_id' => $reel->id,
        'deleted_at' => null,
    ]);
});

it('removeItemFromCollection mutation removes item from a specific collection', function () {
    Notification::fake();
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Collection $collection1 */
    $collection1 = Collection::factory()->for($user)->create();
    /** @var Collection $collection2 */
    $collection2 = Collection::factory()->for($user)->create();
    /** @var Reel $reel */
    $reel = Reel::factory()->create();

    // Add item to both collections first
    $collection1->items()->create(['collectable_id' => $reel->id, 'collectable_type' => Reel::class]);
    $collection2->items()->create(['collectable_id' => $reel->id, 'collectable_type' => Reel::class]);

    assertDatabaseHas('collection_items', ['collection_id' => $collection1->id, 'collectable_id' => $reel->id, 'deleted_at' => null]);
    assertDatabaseHas('collection_items', ['collection_id' => $collection2->id, 'collectable_id' => $reel->id, 'deleted_at' => null]);

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: RemoveItemFromCollectionInput!) {
            removeItemFromCollection(input: $input)
        }
    ', [
        'input' => [
            'collection_ids' => [(string) $collection1->id],
            'collectable_id' => (string) $reel->id,
            'collectable_type' => 'REEL',
        ],
    ]);

    $response->assertJson([
        'data' => [
            'removeItemFromCollection' => true,
        ],
    ]);

    assertSoftDeleted('collection_items', [
        'collection_id' => $collection1->id,
        'collectable_id' => $reel->id,
        'collectable_type' => Reel::class,
    ]);
    assertDatabaseHas('collection_items', [ // Should still exist in the other collection
        'collection_id' => $collection2->id,
        'collectable_id' => $reel->id,
        'collectable_type' => Reel::class,
        'deleted_at' => null, // Ensure it's not soft deleted
    ]);
});

it('removeItemFromCollection mutation removes item from multiple specified collections', function () {
    Notification::fake();
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Collection $collection1 */
    $collection1 = Collection::factory()->for($user)->create();
    /** @var Collection $collection2 */
    $collection2 = Collection::factory()->for($user)->create();
    /** @var Collection $collection3 */
    $collection3 = Collection::factory()->for($user)->create();
    /** @var Reel $reel */
    $reel = Reel::factory()->create();

    // Add item to all three collections
    $collection1->items()->create(['collectable_id' => $reel->id, 'collectable_type' => Reel::class]);
    $collection2->items()->create(['collectable_id' => $reel->id, 'collectable_type' => Reel::class]);
    $collection3->items()->create(['collectable_id' => $reel->id, 'collectable_type' => Reel::class]);

    assertDatabaseHas('collection_items', ['collection_id' => $collection1->id, 'collectable_id' => $reel->id, 'deleted_at' => null]);
    assertDatabaseHas('collection_items', ['collection_id' => $collection2->id, 'collectable_id' => $reel->id, 'deleted_at' => null]);
    assertDatabaseHas('collection_items', ['collection_id' => $collection3->id, 'collectable_id' => $reel->id, 'deleted_at' => null]);

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: RemoveItemFromCollectionInput!) {
            removeItemFromCollection(input: $input)
        }
    ', [
        'input' => [
            'collection_ids' => [(string) $collection1->id, (string) $collection2->id],
            'collectable_id' => (string) $reel->id,
            'collectable_type' => 'REEL',
        ],
    ]);

    $response->assertJson(['data' => ['removeItemFromCollection' => true]]);

    assertSoftDeleted('collection_items', ['collection_id' => $collection1->id, 'collectable_id' => $reel->id, 'collectable_type' => Reel::class]);
    assertSoftDeleted('collection_items', ['collection_id' => $collection2->id, 'collectable_id' => $reel->id, 'collectable_type' => Reel::class]);
    assertDatabaseHas('collection_items', [
        'collection_id' => $collection3->id,
        'collectable_id' => $reel->id,
        'collectable_type' => Reel::class,
        'deleted_at' => null, // Ensure it's not soft deleted
    ]);
});

it('removeItemFromAllCollections mutation removes item from all user collections', function () {
    Notification::fake();
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Collection $collection1 */
    $collection1 = Collection::factory()->for($user)->create();
    /** @var Collection $collection2 */
    $collection2 = Collection::factory()->for($user)->create();
    /** @var Reel $reel */
    $reel = Reel::factory()->create();

    // Add item to both collections
    $collection1->items()->create(['collectable_id' => $reel->id, 'collectable_type' => Reel::class]);
    $collection2->items()->create(['collectable_id' => $reel->id, 'collectable_type' => Reel::class]);

    // Create a collection for another user with the same item, to ensure it's not deleted
    /** @var User $otherUser */
    $otherUser = User::factory()->create();
    /** @var Collection $otherUserCollection */
    $otherUserCollection = Collection::factory()->for($otherUser)->create();
    $otherUserCollection->items()->create(['collectable_id' => $reel->id, 'collectable_type' => Reel::class]);

    assertDatabaseHas('collection_items', ['collection_id' => $collection1->id, 'collectable_id' => $reel->id, 'deleted_at' => null]);
    assertDatabaseHas('collection_items', ['collection_id' => $collection2->id, 'collectable_id' => $reel->id, 'deleted_at' => null]);
    assertDatabaseHas('collection_items', ['collection_id' => $otherUserCollection->id, 'collectable_id' => $reel->id, 'deleted_at' => null]);

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: RemoveItemFromAllCollectionsInput!) {
            removeItemFromAllCollections(input: $input)
        }
    ', [
        'input' => [
            'collectable_id' => (string) $reel->id,
            'collectable_type' => 'REEL',
        ],
    ]);

    $response->assertJson([
        'data' => [
            'removeItemFromAllCollections' => true,
        ],
    ]);

    assertSoftDeleted('collection_items', [
        'collection_id' => $collection1->id,
        'collectable_id' => $reel->id,
        'collectable_type' => Reel::class,
    ]);
    assertSoftDeleted('collection_items', [
        'collection_id' => $collection2->id,
        'collectable_id' => $reel->id,
        'collectable_type' => Reel::class,
    ]);
    assertDatabaseHas('collection_items', [ // Item in other user's collection should remain
        'collection_id' => $otherUserCollection->id,
        'collectable_id' => $reel->id,
        'collectable_type' => Reel::class,
        'deleted_at' => null, // Ensure it's not soft deleted
    ]);
});

it('removeItemFromCollection returns false if item not in collection', function () {
    Notification::fake();
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Collection $collection */
    $collection = Collection::factory()->for($user)->create();
    /** @var Reel $reel */
    $reel = Reel::factory()->create(); // Item not added to collection

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: RemoveItemFromCollectionInput!) {
            removeItemFromCollection(input: $input)
        }
    ', [
        'input' => [
            'collection_ids' => [(string) $collection->id],
            'collectable_id' => (string) $reel->id,
            'collectable_type' => 'REEL',
        ],
    ]);

    $response->assertJson(['data' => ['removeItemFromCollection' => false]]);
});

it('removeItemFromAllCollections returns false if item not in any of users collections', function () {
    Notification::fake();
    /** @var User $user */
    $user = User::factory()->create();
    actingAs($user, 'sanctum');

    /** @var Reel $reel */
    $reel = Reel::factory()->create(); // Item not added to any collection
    // User has collections, but not with this item
    Collection::factory()->for($user)->count(2)->create();

    $response = graphQL(/** @lang GraphQL */ '
        mutation($input: RemoveItemFromAllCollectionsInput!) {
            removeItemFromAllCollections(input: $input)
        }
    ', [
        'input' => [
            'collectable_id' => (string) $reel->id,
            'collectable_type' => 'REEL',
        ],
    ]);

    $response->assertJson(['data' => ['removeItemFromAllCollections' => false]]);
});
