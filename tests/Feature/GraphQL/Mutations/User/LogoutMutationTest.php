<?php

declare(strict_types=1);

use App\Models\User;
use Laravel\Sanctum\Sanctum;

describe('LogoutMutation', function () {
    test('Unauthenticated', function () {

        graphQL(/** @lang GraphQL */ '
        mutation {
            logout {
                status
            }
        }')->assertGraphQLErrorMessage('Unauthenticated.');
    });

    test('Authenticated', function () {

        /** @var User */
        $user = Sanctum::actingAs(User::factory()->create());

        graphQL(/** @lang GraphQL */ '
            mutation {
                logout {
                    status
                }
            }')->assertJson(['data' => ['logout' => ['status' => true]]]);
    });
});
