<?php

use App\Enums\SocialLoginStatus;
use App\Models\Invitation;
use App\Models\User;

use function Pest\Laravel\travelTo;

beforeEach(function () {
    config()->set('services.google.enabled', true);
    mockSocialiteDriver('google');
});

it('creates invitation with correct expiry date', function () {
    $now = now();
    travelTo($now);

    graphQL(/** @lang GraphQL */ '
    mutation ($provider: SocialLoginProvider!, $token: String!) {
        socialLogin(provider: $provider, token: $token) {
            token
            status_code
            message
        }
    }',
        [
            'provider' => 'GOOGLE',
            'token' => 'valid-token',
        ]
    );

    /** @var Invitation $invitation */
    $invitation = Invitation::query()->latest('id')->first();
    expect($invitation)->not->toBeNull()
        ->and($invitation->expires_at->startOfSecond()->toDateTimeString())
        ->toBe($now->addDays((int) config('invitation.expiry_days'))->startOfSecond()->toDateTimeString());
});

it('renews expired invitation', function () {
    /** @var Invitation */
    $invitation = Invitation::factory()
        ->expired()
        ->pending()
        ->create(['email' => '<EMAIL>']);

    expect($invitation->fresh()->isExpired())->toBeTrue();

    $response = graphQL(/** @lang GraphQL */ '
    mutation ($provider: SocialLoginProvider!, $token: String!) {
        socialLogin(provider: $provider, token: $token) {
            token
            user {
                email
            }
            status_code
            message
        }
    }',
        [
            'provider' => 'GOOGLE',
            'token' => 'valid-token',
        ]
    );

    $response->assertJson([
        'data' => [
            'socialLogin' => [
                'status_code' => SocialLoginStatus::PENDING_INVITATION->name,
                'message' => 'Your invitation has been renewed and is pending approval.',
            ],
        ],
    ]);

    /** @var User $user */
    $user = User::where('email', '<EMAIL>')->first();
    expect($user)->not->toBeNull();

    /** @var Invitation $invitation */
    $invitation = $invitation->fresh();
    expect($invitation->isExpired())->toBeFalse();
});

it('keeps invitation valid before expiry date', function () {
    /** @var Invitation */
    $invitation = Invitation::factory()
        ->approved()
        ->create(['email' => '<EMAIL>']);

    expect($invitation->fresh()->isExpired())->toBeFalse();

    $response = graphQL(/** @lang GraphQL */ '
    mutation ($provider: SocialLoginProvider!, $token: String!) {
        socialLogin(provider: $provider, token: $token) {
            token
            user {
                email
            }
            status_code
        }
    }',
        [
            'provider' => 'GOOGLE',
            'token' => 'valid-token',
        ]
    );

    $response->assertJson([
        'data' => [
            'socialLogin' => [
                'status_code' => SocialLoginStatus::NEW_USER->name,
            ],
        ],
    ]);

    /** @var User $user */
    $user = User::where('email', '<EMAIL>')->first();
    expect($user)->not->toBeNull();

    expect(Invitation::find($invitation->id))->not->toBeNull();
});
