<?php

declare(strict_types=1);

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\User;
use App\Models\UserDeal;

// TODO: fails when redeeming an expired deal

it('fails for unauthenticated user', function () {

    /** @var Deal */
    $deal = Deal::factory()->create();
    /** @var UserDeal */
    $myDeal = UserDeal::create([
        'user_id' => '1',
        'deal_id' => $deal->id,
        'reserve_slot' => [
            'date' => now()->addDay()->toDateString(),
            'slot' => [
                'from' => '13:00',
                'to' => '14:00',
            ],
        ],
    ]);

    $response = graphQL(/** @lang GraphQL */ '
    mutation ($myDealId: ID!) {
        redeemDeal (input: {
            myDealId: $myDealId
        }) {
            myDeal {
                id
            }
        }
    }', [
        'myDealId' => $myDeal->id,
    ]);

    $response->assertGraphQLErrorMessage('Unauthenticated.');
});

it('fails with invalid deal ID', function (User $user, Deal $deal) {
    \Pest\Laravel\actingAs($user, 'sanctum');
    $userDeal = UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'reserve_slot' => [
            'date' => now()->toDateString(),
            'slot' => [
                'from' => '13:00',
                'to' => '14:00',
            ],
        ],
    ]);

    $response = graphQL(/** @lang GraphQL */ '
        mutation {
            redeemDeal (input: {
                myDealId: "invalid_id"
            }) {
                myDeal {
                    id
                }
            }
        }
    ');

    $response->assertGraphQLErrorMessage('Validation failed for the field [redeemDeal].');
})->with('deal');

it('redeems a deal successfully', function (User $user, Deal $deal) {
    \Pest\Laravel\actingAs($user, 'sanctum');
    $userDeal = UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'status' => 'redeemable',
        'reserve_slot' => [
            'date' => now()->toDateString(),
            'slot' => [
                'from' => '13:00',
                'to' => '14:00',
            ],
        ],
    ]);

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($myDealId: ID!) {
            redeemDeal (input: {
                myDealId: $myDealId
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    redeemed_at
                }
            }
        }
    ', [
        'myDealId' => $userDeal->id,
    ]);

    ray($response->json());
    $response->assertJsonStructure([
        'data' => [
            'redeemDeal' => [
                'myDeal' => [
                    'id',
                    'status',
                    'deal' => [
                        'id',
                    ],
                    'redeemed_at',
                ],
            ],
        ],
    ]);

    $redeemedAt = $response->json('data.redeemDeal.myDeal.redeemed_at');
    $this->assertNotNull($redeemedAt);

    expect($response->json('data.redeemDeal.myDeal.status'))->toBe('REDEEMED');
})->with('deal');

it('fails when redeeming an already redeemed deal', function (User $user, Deal $deal) {
    \Pest\Laravel\actingAs($user, 'sanctum');

    $userDeal = UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'redeemed_at' => now(),
        'reserve_slot' => [
            'date' => now()->toDateString(),
            'slot' => [
                'from' => '13:00',
                'to' => '14:00',
            ],
        ],
    ]);

    $userDeal->update(['redeemed_at' => now()]);

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($myDealId: ID!) {
            redeemDeal (input: {
                myDealId: $myDealId
            }) {
                myDeal {
                    id
                }
            }
        }
    ', [
        'myDealId' => $userDeal->id,
    ]);

    $response->assertGraphQLErrorMessage('Validation failed for the field [redeemDeal].');
})->with('deal')->todo('With Hazem');

it('fails when trying to redeem someone else\'s deal', function (User $user, Deal $deal) {
    \Pest\Laravel\actingAs($user, 'sanctum');
    $userDeal = UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'reserve_slot' => [
            'date' => now()->toDateString(),
            'slot' => [
                'from' => '13:00',
                'to' => '14:00',
            ],
        ],
    ]);

    $otherUser = User::factory()->create();

    $otherMyDeal = UserDeal::create([
        'user_id' => $otherUser->id,
        'deal_id' => $deal->id,
    ]);

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($myDealId: ID!) {
            redeemDeal (input: {
                myDealId: $myDealId
            }) {
                myDeal {
                    id
                }
            }
        }
    ', [
        'myDealId' => $otherMyDeal->id,
    ]);

    $response->assertGraphQLErrorMessage('You do not have permission to redeem this deal.');
})->with('deal');

dataset('deal', [
    [
        fn () => User::factory()->create(),
        fn () => Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->addDay()->day,
            ]))
            ->create(),
    ],
]);
