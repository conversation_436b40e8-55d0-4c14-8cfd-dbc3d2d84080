<?php

declare(strict_types=1);

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\User;
use App\Models\UserDeal;
use Illuminate\Support\Facades\Cache;
use Laravel\Sanctum\Sanctum;

// TODO: Fail to reserve with no available seats
// TODO: Success to renew a deal

it('fails for unauthenticated user', function () {

    /** @var Deal */
    $deal = Deal::factory()->create();

    $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                }
            }
        }
    ', [
        'id' => $deal->id,
        'date' => now()->addDay()->format('Y-m-d'),
        'from' => '00:00',
        'to' => '02:00',
    ]);

    $response->assertGraphQLErrorMessage('Unauthenticated.');
});

describe('ReserveDealMutation', function () {

    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        Sanctum::actingAs($this->user);
    });

    test('valid Deal ID with authenticated user', function () {

        graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal(
                    input: {id: $id, reserve_slot: {date: $date, slot: {from: $from, to: $to}}, myDealIdToRenew: null}
                ) {
                    myDeal {
                        id
                        status
                    }
                }
            }',
            [
                'id' => $this->deal->id,
                'date' => now()->addDay()->format('Y-m-d'),
                'from' => now()->addDay()->format('Y-m-d 00:00'),
                'to' => now()->addDay()->format('Y-m-d 02:00'),
            ])->assertJson([
                'data' => [
                    'reserveDeal' => [
                        'myDeal' => [
                            'id' => UserDeal::latest('id')->value('id'),
                            'status' => 'UPCOMING',
                        ],
                    ],
                ],
            ]);
    });

    it('fails with invalid deal ID', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation {
            reserveDeal (input: {
                id: "invalid_id",
                reserve_slot: {
                    date: "2025-04-05",
                    slot: {
                        from: "00:00",
                        to: "02:00"
                    }
                }
            }) {
                myDeal {
                    id
                }
            }
        }');

        $response->assertGraphQLErrorMessage('Validation failed for the field [reserveDeal].');
    });

    it('reserves a deal successfully', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    reserved_at
                    reserve_slot {
                        date
                        slot {
                            from
                            to
                        }
                    }
                }
            }
        }', [
            'id' => $this->deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ]);

        $response->assertJsonStructure([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id',
                        'status',
                        'deal' => [
                            'id',
                        ],
                        'reserved_at',
                        'reserve_slot' => [
                            'date',
                            'slot' => [
                                'from',
                                'to',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $reservedAt = $response->json('data.reserveDeal.myDeal.reserved_at');
        $this->assertNotNull($reservedAt);

        expect($response->json('data.reserveDeal.myDeal.status'))->toBe('UPCOMING');
    });

    it('reserves a deal in same slots day successfully before slot time comes', function () {
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '14:00',
                'to' => '18:00',
                'day' => now()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        \Carbon\Carbon::setTestNow(now()->toDateString().' 13:00');

        $response = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                        reserved_at
                        reserve_slot {
                            date
                            slot {
                                from
                                to
                            }
                        }
                    }
                }
            }', [
            'id' => $deal->id,
            'date' => now()->format('Y-m-d'),
            'from' => now()->format('Y-m-d 14:00'),
            'to' => now()->format('Y-m-d 18:00'),
        ]);

        $response->assertJsonStructure([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id',
                        'status',
                        'deal' => [
                            'id',
                        ],
                        'reserved_at',
                        'reserve_slot' => [
                            'date',
                            'slot' => [
                                'from',
                                'to',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $reservedAt = $response->json('data.reserveDeal.myDeal.reserved_at');
        $this->assertNotNull($reservedAt);

        expect($response->json('data.reserveDeal.myDeal.status'))->toBe('UPCOMING');
    });

    it('reserves a deal in same slots day successfully after slot time comes', function () {
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '14:00',
                'to' => '18:00',
                'day' => now()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        \Carbon\Carbon::setTestNow(now()->toDateString().' 19:00');

        $response = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                        reserved_at
                        reserve_slot {
                            date
                            slot {
                                from
                                to
                            }
                        }
                    }
                }
            }', [
            'id' => $deal->id,
            'date' => now()->format('Y-m-d'),
            'from' => now()->format('Y-m-d 14:00'),
            'to' => now()->format('Y-m-d 18:00'),
        ])->assertGraphQLErrorMessage('Invalid reserve slot. The date must be in the future.');
    });

    it('reserves a deal in same slots day successfully between slot time comes', function () {
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '14:00',
                'to' => '18:00',
                'day' => now()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        \Carbon\Carbon::setTestNow(now()->toDateString().' 16:00');

        $response = graphQL(/** @lang GraphQL */ '
            mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
                reserveDeal (input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }) {
                    myDeal {
                        id
                        status
                        deal {
                            id
                        }
                        reserved_at
                        reserve_slot {
                            date
                            slot {
                                from
                                to
                            }
                        }
                    }
                }
            }', [
            'id' => $deal->id,
            'date' => now()->format('Y-m-d'),
            'from' => now()->format('Y-m-d 14:00'),
            'to' => now()->format('Y-m-d 18:00'),
        ]);

        $response->assertJsonStructure([
            'data' => [
                'reserveDeal' => [
                    'myDeal' => [
                        'id',
                        'status',
                        'deal' => [
                            'id',
                        ],
                        'reserved_at',
                        'reserve_slot' => [
                            'date',
                            'slot' => [
                                'from',
                                'to',
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $reservedAt = $response->json('data.reserveDeal.myDeal.reserved_at');
        $this->assertNotNull($reservedAt);

        expect($response->json('data.reserveDeal.myDeal.status'))->toBe('REDEEMABLE');
    });

    it('Fail to reserve with no available seats', function () {
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->addDay()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 1,
            ]);

        UserDeal::factory()->create([
            'user_id' => User::factory()->create()->id,
            'deal_id' => $deal->id,
            'reserve_slot' => [
                'date' => now()->addDay()->format('Y-m-d'),
                'slot' => [
                    'from' => '00:00',
                    'to' => '02:00',
                ],
            ],
        ]);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    reserved_at
                    reserve_slot {
                        date
                        slot {
                            from
                            to
                        }
                    }
                }
            }
        }', [
            'id' => $deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
        ])->assertGraphQLErrorMessage('There is no available seats left for this deal');
    });

    it('fails with invalid time slot', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                }
            }
        }', [
            'id' => $this->deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 23:00'),
            'to' => now()->addDay()->format('Y-m-d 01:00'),
        ]);

        $response->assertGraphQLErrorMessage('Invalid reserve slot. The slot is not available for this deal');
    });

    it('fails with past date reservation', function () {
        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal(
                input: {
                    id: $id,
                    reserve_slot: {
                        date: $date,
                        slot: {
                            from: $from,
                            to: $to
                        }
                    }
                }
            ) {
                myDeal {
                    id
                }
            }
        }', [
            'id' => $this->deal->id,
            'date' => now()->subDay()->format('Y-m-d'),
            'from' => now()->subDay()->format('Y-m-d 00:00'),
            'to' => now()->subDay()->format('Y-m-d 02:00'),
        ]);

        $response->assertGraphQLErrorMessage('Invalid reserve slot. The date must be in the future.');
    });

    it('fails when trying to renew a reserved deal', function () {
        $myDeal = UserDeal::create([
            'user_id' => $this->user->id,
            'deal_id' => $this->deal->id,
            'reserved_at' => now(),
            'reserve_slot' => [
                'date' => now()->addDay()->format('Y-m-d'),
                'slot' => [
                    'from' => '00:00',
                    'to' => '02:00',
                ],
            ],
        ]);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!, $myDealIdToRenew: ID!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                },
                myDealIdToRenew: $myDealIdToRenew
            }) {
                myDeal {
                    id
                }
            }
        }
    ', [
            'id' => $this->deal->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'from' => now()->addDay()->format('Y-m-d 00:00'),
            'to' => now()->addDay()->format('Y-m-d 02:00'),
            'myDealIdToRenew' => $myDeal->id,
        ]);

        $response->assertGraphQLErrorMessage('Can\'t renew this deal before x days.');
    })->todo('With @Hazem');

    it('it cache my deal query and flush after mutation', function () {
        \Carbon\Carbon::setTestNow(now()->toDateString().' 01:00:00');
        /** @var Deal $deal */
        $deal = Deal::factory()
            ->has(DealSlot::factory()->state(fn (array $attributes) => [
                'from' => '00:00',
                'to' => '02:00',
                'day' => now()->format('w'),
            ]))
            ->create([
                'reuse_limit_days' => 7,
                'max_usage_per_day' => 10,
            ]);

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($id: ID!, $date: Date!, $from: String!, $to: String!) {
            reserveDeal (input: {
                id: $id,
                reserve_slot: {
                    date: $date,
                    slot: {
                        from: $from,
                        to: $to
                    }
                }
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    reserved_at
                    reserve_slot {
                        date
                        slot {
                            from
                            to
                        }
                    }
                }
            }
        }', [
            'id' => $deal->id,
            'date' => now()->toDateString(),
            'from' => now()->format('Y-m-d 00:00'),
            'to' => now()->format('Y-m-d 02:00'),
        ]);

        expect($response)->toBeObject()
            ->and($response->json('data.reserveDeal.myDeal.status'))->toBe('REDEEMABLE');

        $myDealId = $response->json('data.reserveDeal.myDeal.id');

        // query myDeal by id
        graphQL(/** @lang GraphQL */ '
                query ($id: ID!) {
                    myDeal(id: $id) {
                        id
                        status
                    }
                }
            ', ['id' => (int) $myDealId])
            ->assertJson([
                'data' => [
                    'myDeal' => [
                        'id' => $myDealId,
                        'status' => 'REDEEMABLE',
                    ],
                ],
            ]);

        // lighthouse:auth:28:Query::myDeal:id:1
        expect(Cache::get("lighthouse:auth:{$this->user->id}:Query::myDeal:id:$myDealId"))->not->toBeNull();

        /** @var UserDeal $userDeal */
        $userDeal = UserDeal::query()->latest('id')->first();

        $response = graphQL(/** @lang GraphQL */ '
        mutation ($myDealId: ID!) {
            redeemDeal (input: {
                myDealId: $myDealId
            }) {
                myDeal {
                    id
                    status
                    deal {
                        id
                    }
                    redeemed_at
                }
            }
        }
    ', [
            'myDealId' => $userDeal->id,
        ]);

        // dd($response->json());
        expect($response->json('data.redeemDeal.myDeal.status'))
            ->toBe('REDEEMED');

        expect(Cache::get("lighthouse:auth:{$this->user->id}:Query::myDeal:id:$myDealId"))->toBeNull();
    })->group('cache')->skip('Disable cache test for now');
});
