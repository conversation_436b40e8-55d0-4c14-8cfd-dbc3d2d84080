<?php

declare(strict_types=1);

use App\Models\Deal;
use App\Models\DealSlot;
use App\Models\User;
use App\Models\UserDeal;

// TODO: fails when cancelling after cancellation deadline

it('fails for unauthenticated user', function () {
    /** @var Deal */
    $deal = Deal::factory()->create();
    /** @var UserDeal */
    $myDeal = UserDeal::create([
        'user_id' => '1',
        'deal_id' => $deal->id,
        'reserve_slot' => [
            'date' => now()->toDateString(),
            'slot' => [
                'from' => '13:00',
                'to' => '14:00',
            ],
        ],
    ]);

    graphQL(/** @lang GraphQL */
        'mutation ($id: ID!){
            cancelDeal(
                input: {myDealId: $id}
            ) {
                message
            }
        }', ['id' => $myDeal->id])
        ->assertGraphQLErrorMessage('Unauthenticated.');
});

describe('CancelDealMutation', function () {
    it('fails with invalid deal ID', function (User $user, Deal $deal) {
        $response = graphQL(/** @lang GraphQL */
            'mutation ($id: ID!){
            cancelDeal(
                input: {myDealId: $id}
            ) {
                message
            }
        }', ['id' => 'invalid_id']);

        $response->assertGraphQLErrorMessage('Validation failed for the field [cancelDeal].');
    })->with('deal');

    it('cancels a deal successfully', function (User $user, Deal $deal) {
        \Pest\Laravel\actingAs($user, 'sanctum');

        $userDeal = UserDeal::create([
            'user_id' => $user->id,
            'deal_id' => $deal->id,
            'reserve_slot' => [
                'date' => now()->addDay()->toDateString(),
                'slot' => [
                    'from' => '13:00',
                    'to' => '14:00',
                ],
            ],
        ]);

        $response = graphQL(/** @lang GraphQL */
            'mutation ($id: ID!) {
            cancelDeal(
                input: {myDealId: $id}
            ) {
                message
            }
        }', ['id' => $userDeal->id]);

        $response->assertJsonStructure([
            'data' => [
                'cancelDeal' => [
                    'message',
                ],
            ],
        ]);

        $message = $response->json('data.cancelDeal.message');
        $this->assertEquals('Deal cancelled successfully', $message);
    })->with('deal');

    it('fails when cancelling an already cancelled deal', function (User $user, Deal $deal) {
        \Pest\Laravel\actingAs($user, 'sanctum');

        $userDeal = UserDeal::create([
            'user_id' => $user->id,
            'deal_id' => $deal->id,
            'reserve_slot' => [
                'date' => now()->addDay()->toDateString(),
                'slot' => [
                    'from' => '13:00',
                    'to' => '14:00',
                ],
            ],
        ]);

        $response = graphQL(/** @lang GraphQL */
            'mutation ($id: ID!){
                        cancelDeal(
                            input: {myDealId: $id}
                        ) {
                            message
                        }
                    }',
            ['id' => $userDeal->id])
            ->assertJsonStructure([
                'data' => [
                    'cancelDeal' => [
                        'message',
                    ],
                ],
            ]);

        $message = $response->json('data.cancelDeal.message');

        $this->assertEquals('Deal cancelled successfully', $message);

        graphQL(/** @lang GraphQL */
            'mutation ($id: ID!){
                        cancelDeal(
                            input: {myDealId: $id}
                        ) {
                            message
                        }
                    }',
            ['id' => $userDeal->id])
            ->assertGraphQLErrorMessage('Validation failed for the field [cancelDeal].');
    })->with('deal');

    it('fails when cancelling an already redeemed deal', function (User $user, Deal $deal) {
        \Pest\Laravel\actingAs($user, 'sanctum');

        $userDeal = UserDeal::create([
            'user_id' => $user->id,
            'deal_id' => $deal->id,
            'redeemed_at' => now(),
            'reserve_slot' => [
                'date' => now()->addDay()->toDateString(),
                'slot' => [
                    'from' => '13:00',
                    'to' => '14:00',
                ],
            ],
        ]);

        $response = graphQL(/** @lang GraphQL */
            'mutation ($id: ID!){
            cancelDeal(
                input: {myDealId: $id}
            ) {
                message
            }
        }', ['id' => $userDeal->id]);

        $response->assertGraphQLErrorMessage('Deal is already redeemed.');
    })->with('deal');

    it('fails when trying to cancel someone else\'s deal', function (User $user, Deal $deal) {
        \Pest\Laravel\actingAs($user, 'sanctum');

        $otherUser = User::factory()->create();
        /** @var UserDeal */
        $otherMyDeal = UserDeal::create([
            'user_id' => $otherUser->id,
            'deal_id' => $deal->id,
            'reserve_slot' => [
                'date' => now()->addDay()->toDateString(),
                'slot' => [
                    'from' => '13:00',
                    'to' => '14:00',
                ],
            ],
        ]);

        $response = graphQL(/** @lang GraphQL */
            'mutation ($id: ID!){
            cancelDeal(
                input: {myDealId: $id}
            ) {
                message
            }
        }', ['id' => $otherMyDeal->id]);

        $response->assertGraphQLErrorMessage('You do not have permission to cancel this deal.');
    })->with('deal');

    dataset('deal', [
        [
            fn () => User::factory()->create(),
            fn () => Deal::factory()
                ->has(DealSlot::factory()->state(fn (array $attributes) => [
                    'from' => '00:00',
                    'to' => '02:00',
                    'day' => now()->addDay()->day,
                ]))
                ->create(),
        ],
    ]);
});
