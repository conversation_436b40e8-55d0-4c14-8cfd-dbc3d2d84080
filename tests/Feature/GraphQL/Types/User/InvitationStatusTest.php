<?php

declare(strict_types=1);

use App\Models\Invitation;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

describe('User Status Field', function () {
    test('returns SUCCESS when user has no invitation', function () {
        $user = User::factory()->create();
        \Pest\Laravel\actingAs($user, 'sanctum');

        $response = graphQL(/** @lang GraphQL */ '
            query {
                me {
                    status
                }
            }
        ');

        $response->assertJson([
            'data' => [
                'me' => [
                    'status' => 'SUCCESS',
                ],
            ],
        ]);
    });

    test('returns EXPIRED_INVITATION when invitation is expired', function () {
        $user = User::factory()->create();
        Invitation::factory()
            ->expired()
            ->create(['email' => $user->email]);

        \Pest\Laravel\actingAs($user, 'sanctum');

        $response = graphQL(/** @lang GraphQL */ '
            query {
                me {
                    status
                }
            }
        ');

        $response->assertJson([
            'data' => [
                'me' => [
                    'status' => 'EXPIRED_INVITATION',
                ],
            ],
        ]);
    });

    test('returns AWAITING_APPROVAL when invitation is pending', function () {
        $user = User::factory()->create();
        Invitation::factory()
            ->pending()
            ->create(['email' => $user->email]);

        \Pest\Laravel\actingAs($user, 'sanctum');

        $response = graphQL(/** @lang GraphQL */ '
            query {
                me {
                    status
                }
            }
        ');

        $response->assertJson([
            'data' => [
                'me' => [
                    'status' => 'AWAITING_APPROVAL',
                ],
            ],
        ]);
    });

    test('returns SUCCESS when invitation is approved', function () {
        $user = User::factory()->create();
        Invitation::factory()
            ->approved()
            ->create(['email' => $user->email]);

        \Pest\Laravel\actingAs($user, 'sanctum');

        $response = graphQL(/** @lang GraphQL */ '
            query {
                me {
                    status
                }
            }
        ');

        $response->assertJson([
            'data' => [
                'me' => [
                    'status' => 'SUCCESS',
                ],
            ],
        ]);
    });
});
