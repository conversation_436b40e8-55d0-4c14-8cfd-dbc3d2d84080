<?php

declare(strict_types=1);

use App\Models\Deal;
use App\Models\User;
use App\Models\UserDeal;

test('MyDeal', function () {
    /** @var \App\Models\User $user */
    $user = User::factory()->create();

    /** @var \App\Models\Deal $deal */
    $deal = Deal::factory()
        ->create();

    // We are creating deals for different users with the same deal and
    // the same user with different deals. The purpose of this test is
    // to ensure that only the requested deal associated with the authenticated
    // user is returned.

    UserDeal::create([
        'user_id' => User::factory()->create()->id,
        'deal_id' => $deal->id,
    ]);

    UserDeal::create([
        'user_id' => User::factory()->create()->id,
        'deal_id' => $deal->id,
    ]);

    /** @var \App\Models\UserDeal $myDealId */
    $myDealId = UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
    ]);

    /** @var \App\Models\Deal $deal2 */
    $deal2 = Deal::factory()->create();

    /** @var \App\Models\UserDeal $myDealId2 */
    $myDealId2 = UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal2->id,
    ]);

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        query MyDeals{
               deals(first: 30) {
                  data {
                    id
                    myDeals(first: 10) {
                      data {
                        id
                        deal {
                          id
                        }
                        reserved_at
                      }
                    }
                  }
                }
        }'
    )->assertJson([
        'data' => [
            'deals' => [
                'data' => [
                    [
                        'id' => $deal->id,
                        'myDeals' => [
                            'data' => [
                                [
                                    'id' => $myDealId->id, // pivot id = myDealId
                                    'reserved_at' => null,
                                    'deal' => [
                                        'id' => $deal->id,
                                    ],
                                ],
                            ],
                        ],
                    ],
                    [
                        'id' => $deal2->id,
                        'myDeals' => [
                            'data' => [
                                [
                                    'id' => $myDealId2->id, // pivot id = myDealId2
                                    'reserved_at' => null,
                                    'deal' => [
                                        'id' => $deal2->id,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ]);
});

test('MyDeal orders by JSON date', function () {
    /** @var \App\Models\User $user */
    $user = User::factory()->create();

    /** @var \App\Models\Deal $deal */
    $deal = Deal::factory()->create();

    // Create UserDeals with JSON data containing different dates
    UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'reserve_slot' => ['date' => '2025-04-21', 'slot' => ['from' => '19:33:16', 'to' => '21:33:16']],
    ]);

    UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'reserve_slot' => ['date' => '2025-04-20', 'slot' => ['from' => '18:00:00', 'to' => '20:00:00']],
    ]);

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        query MyDeals($orderBy: [OrderByClause!]) {
        deals(first: 10) {
            data {
              id
              myDeals(first: 10, orderBy: $orderBy) {
                data {
                  id
                reserve_slot {
                        date
                        slot {
                            from
                            to
                        }
                    }
                }
              }
            }
        }
        }', [
        'orderBy' => [
            ['column' => 'reserve_slot', 'order' => 'ASC'],
        ],
    ]);

    // dd($response->json());
    $response->assertJsonPath('data.deals.data.0.myDeals.data.0.reserve_slot.date', '2025-04-20');
    $response->assertJsonPath('data.deals.data.0.myDeals.data.1.reserve_slot.date', '2025-04-21');
});

test('MyDeal orders by regular column', function () {
    /** @var \App\Models\User $user */
    $user = User::factory()->create();

    /** @var \App\Models\Deal $deal */
    $deal = Deal::factory()->create();

    // Create UserDeals with different reserved_at values
    UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'reserved_at' => '2025-04-15 10:00:00',
    ]);

    UserDeal::create([
        'user_id' => $user->id,
        'deal_id' => $deal->id,
        'reserved_at' => '2025-04-16 12:00:00',
    ]);

    \Pest\Laravel\actingAs($user, 'sanctum');

    $response = graphQL(/** @lang GraphQL */ '
        query MyDeals($orderBy: [OrderByClause!]) {
            deals(first: 10) {
                data {
                    id
                    myDeals(first: 10, orderBy: $orderBy) {
                        data {
                            id
                            reserved_at
                        }
                    }
                }
            }
        }', [
        'orderBy' => [
            ['column' => 'reserved_at', 'order' => 'ASC'],
        ],
    ]);

    $response->assertJsonPath('data.deals.data.0.myDeals.data.0.reserved_at', '2025-04-15 10:00:00');
    $response->assertJsonPath('data.deals.data.0.myDeals.data.1.reserved_at', '2025-04-16 12:00:00');
});
