#!/bin/sh

 # Run Laravel Pint
 # This script will run Laravel Pint on newly staged PHP Files. 

 files=$(git diff --cached --name-only --diff-filter=AMCR | grep "\.php$")
 if echo "$files" | grep --quiet "\.php$"; then
     echo "Running Laravel Pint..."
     ./vendor/bin/pint $files

     echo "Running Prettier..."
     npx prettier $files -w --ignore-unknown
     
     echo "Running PHPStan..."
     if ! ./vendor/bin/phpstan analyse $files; then
         echo "❌ PHPStan check failed. Please fix the errors before committing."
         exit 1
     fi
    
     git add $files
 fi
