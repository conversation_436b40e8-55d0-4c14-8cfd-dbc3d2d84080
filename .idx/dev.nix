# To learn more about how to use <PERSON> to configure your environment
# see: https://firebase.google.com/docs/studio/customize-workspace
{ pkgs, ... }: {
  # Which nixpkgs channel to use.
  channel = "stable-24.11"; # or "unstable"

  # Use https://search.nixos.org/packages to find packages
  packages = [
    pkgs.php84
    pkgs.php84Packages.composer
    pkgs.nodejs_20
    pkgs.sqlite
  ];
  # Sets environment variables in the workspace
  env = {};

  # See: https://wiki.nixos.org/wiki/Mysql
  # services.mysql = {
  #   enable = true;
  #   package = pkgs.mysql80;
  # };

  idx = {
    # Search for the extensions you want on https://open-vsx.org/ and use "publisher.id"
    extensions = [
      "laravel.vscode-laravel"
      "open-southeners.laravel-pint"
      "esbenp.prettier-vscode"
      "streetsidesoftware.code-spell-checker"
      "m1guelpf.better-pest"
    ];

    # Enable previews and customize configuration
    previews = {
      enable = true;
      previews = {
        web = {
          command = ["php" "artisan" "serve" "--port" "$PORT" "--host" "0.0.0.0"];
          manager = "web";
        };
      };
    };

    # Workspace lifecycle hooks
    workspace = {
      # Runs when a workspace is first created
      onCreate = {
        database-setup = ''
          composer install
          cp .env.example .env && 
          php artisan key:generate
          sed -i 's/DB_CONNECTION=mysql/DB_CONNECTION=sqlite/g;' .env && 
          sed -i 's?DB_DATABASE=conari?DB_DATABASE='`pwd`'/.idx/.data/database.sqlite?g;' .env
          mkdir -p .idx/.data &&
          php artisan migrate --seed --force
        '';
      };
      # Runs when the workspace is (re)started
      onStart = {
        # Example: start a background task to watch and re-build backend code
        # watch-backend = "composer dev";
      };
    };
  };
}
