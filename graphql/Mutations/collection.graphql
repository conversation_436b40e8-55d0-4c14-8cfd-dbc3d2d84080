extend type Mutation {
    createCollection(input: CreateCollectionInput!): Collection!
        @guard
        @field(
            resolver: "App\\GraphQL\\Mutations\\Collection\\CreateCollectionMutation"
        )
    addItemToCollection(input: AddItemToCollectionInput!): [Collection!]!
        @guard
        @field(
            resolver: "App\\GraphQL\\Mutations\\Collection\\AddCollectionItemMutation"
        )

    deleteCollection(id: ID!): Boolean!
        @guard
        @field(
            resolver: "App\\GraphQL\\Mutations\\Collection\\DeleteCollectionMutation"
        )

    editCollection(input: EditCollectionInput!): Collection!
        @guard
        @field(
            resolver: "App\\GraphQL\\Mutations\\Collection\\EditCollectionMutation"
        )

    removeItemFromCollection(input: RemoveItemFromCollectionInput!): Boolean!
        @guard
        @field(
            resolver: "App\\GraphQL\\Mutations\\Collection\\RemoveItemFromCollectionMutation"
        )

    removeItemFromAllCollections(
        input: RemoveItemFromAllCollectionsInput!
    ): Boolean!
        @guard
        @field(
            resolver: "App\\GraphQL\\Mutations\\Collection\\RemoveItemFromAllCollectionsMutation"
        )
}

input CreateCollectionInput {
    title: String!
    description: String
}

input EditCollectionInput {
    id: ID!
    title: String
    description: String
}

input RemoveItemFromCollectionInput {
    collection_ids: [ID!]!
    collectable_id: ID!
    collectable_type: CollectableType!
}

input AddItemToCollectionInput {
    collection_ids: [ID!] # Changed from collection_id: ID
    collectable_id: ID!
    collectable_type: CollectableType = PARTNER_LOCATION
}

input RemoveItemFromAllCollectionsInput {
    collectable_id: ID!
    collectable_type: CollectableType!
}

enum CollectableType {
    REEL
    CREATOR
    PARTNER_LOCATION
}
