extend type Mutation @guard {
    reserveDeal(input: ReserveDealInput!): ReserveDealResponse!
        @field(resolver: "App\\GraphQL\\Mutations\\Deal\\ReserveDealMutation")

    redeemDeal(input: RedeemDealInput!): RedeemDealResponse!
        @field(resolver: "App\\GraphQL\\Mutations\\Deal\\RedeemDealMutation")

    cancelDeal(input: CancelDealInput!): CancelDealResponse!
        @field(resolver: "App\\GraphQL\\Mutations\\Deal\\CancelDealMutation")
}

input ReserveDealInput {
    id: ID! @rules(apply: ["exists:\\App\\Models\\Deal,id"])
    reserve_slot: MyDealDateTimeSlotInput!
    myDealIdToRenew: ID
}

input RedeemDealInput {
    myDealId: ID! @rules(apply: ["exists:\\App\\Models\\UserDeal,id"])
}

input CancelDealInput {
    myDealId: ID! @rules(apply: ["exists:\\App\\Models\\UserDeal,id"])
}

input MyDealDateTimeSlotInput {
    date: Date!
    slot: DealTimeSlotInput!
}

input DealTimeSlotInput {
    from: String!
    to: String!
}

type ReserveDealResponse {
    myDeal: MyDeal!
}

type RedeemDealResponse {
    myDeal: MyDeal!
}

type CancelDealResponse {
    message: String!
}
