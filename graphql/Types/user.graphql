type User {
    "Unique primary key."
    id: ID!

    "Non-unique name."
    name: String!

    "Unique email address."
    email: String!

    "When the email was verified."
    email_verified_at: DateTime

    "When the account was created."
    created_at: DateTime

    "When the account was last updated."
    updated_at: DateTime

    "The creator profile associated with this user, if any"
    creator: Creator @hasOne

    status: LoginStatusCode
        @field(resolver: "App\\GraphQL\\Types\\User\\Status")
}

#Login
type SocialLoginResponse {
    token: String!
    user: User!
    message: String!
    status_code: LoginStatusCode
}

type LogoutResponse {
    status: Boolean!
}

enum SocialLoginProvider {
    GOOGLE @enum(value: "google")
    APPLE @enum(value: "apple")
}

enum LoginStatusCode {
    SUCCESS @enum(value: 200)
    NEW_USER @enum(value: 201)
    PENDING_INVITATION @enum(value: 202)
    AWAITING_APPROVAL @enum(value: 203)
    EXPIRED_INVITATION @enum(value: 410)
}
