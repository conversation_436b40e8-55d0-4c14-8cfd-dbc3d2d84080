type Creator @model(class: "App\\Models\\Creator") {
    id: ID!
    name: String!
    username: String!
    user: User!

    bio: String
    instagram_url: String
    tiktok_url: String
    twitter_url: String
    facebook_url: String
    website_url: String
    website: String

    followers: [User]!
        @belongsToMany(relation: "following_users", type: PA<PERSON>NATOR)

    is_followed: Boolean
        @field(resolver: "App\\GraphQL\\Queries\\Creator\\IsFollowedResolver")
    #@cache(maxAge: 600)

    avatar: Media @morphOne(relation: "avatar") #@cache(maxAge: 3600)
}

extend type User {
    following_creators: [Creator!]
        @belongsToMany(relation: "following_creators", type: PA<PERSON>NATOR)
}

extend type Reel {
    is_creator_followed: Boolean! #@cache(maxAge: 600)
}
