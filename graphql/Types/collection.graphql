type Collection @model(class: "App\\Models\\Collection") {
    id: ID!
    title: String!
    description: String
    user: User!
    items: [CollectionItem]!
    # The is_item_exists field will now use a custom resolver
    is_item_exists(
        collectable_id: ID
        collectable_type: CollectableType
    ): Bo<PERSON>an
        @field(
            resolver: "App\\GraphQL\\Resolvers\\Collection\\IsItemExistsResolver"
        )
    created_at: DateTime!
    updated_at: DateTime!
    myRole: CollectionUserRoleEnum! @method
}

type CollectionItem {
    id: ID!
    collection: Collection!
    collectable_type: String!
    collectable_id: ID!
    collectable: Collectable!
    created_at: DateTime!
    updated_at: DateTime!
}

extend type Reel {
    is_place_in_collection: Boolean!
    saved_places_count: Int!
}

extend type PartnerPlace {
    is_place_in_collection: Boolean!
    saved_places_count: Int!
}

union Collectable = Reel | Creator | PartnerPlace

enum CollectionUserRoleEnum {
    OWNER
    COLLABORATOR
    BOOKMARK
    FALSE
}
