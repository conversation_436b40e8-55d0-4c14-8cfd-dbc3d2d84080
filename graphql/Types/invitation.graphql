type Invitation @model(class: "App\\Models\\Invitation") {
    id: ID!
    email: String!
    name: String!
    provider: String!
    provider_id: String!
    status: InvitationStatus!
    expires_at: DateTime!
    inviter_user_id: ID
    invitee_user_id: ID
    token: String
    token_expires_at: DateTime
    is_used: Boolean!
    created_at: DateTime!
    updated_at: DateTime!

    # Relationships
    inviter: User @belongsTo(relation: "inviter")
    invitee: User @belongsTo(relation: "invitee")
}

enum InvitationStatus {
    PENDING @enum(value: "pending")
    APPROVED @enum(value: "approved")
    REJECTED @enum(value: "rejected")
}

type InvitationInfo {
    total: Int!
    remaining: Int!
}

type UserInvitations {
    data: [Invitation!]!
    info: InvitationInfo!
}

type AcceptInvitationResponse {
    user: User!
    statusCode: Int!
}
