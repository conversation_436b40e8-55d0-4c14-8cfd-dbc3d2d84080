type Reel @model(class: "App\\Models\\Reel") {
    id: ID! @cacheKey
    caption: String
    full_url: String!
    thumbnail: String
    created_at: DateTime!
    updated_at: DateTime!
    creator: CanUploadReel @morphTo
}

extend type PartnerPlace {
    reels: [Reel!]! @belongsToMany(relation: "reelsByPlace", type: <PERSON><PERSON><PERSON>TOR)
}

extend type Creator {
    reels: [Reel!] @morphMany(relation: "reels", type: PA<PERSON>NATOR)
    #@cache(maxAge: 900)
}

union CanUploadReel = Creator | Admin
