extend type Query {
    deal(
        id: ID! @rules(apply: ["exists:\\App\\Models\\Deal,id"]) @whereKey
    ): Deal! @find

    deals: [Deal!]! @paginate #@cache(maxAge: 60)
}

extend type Query @guard {
    """
    MyDeal will be cached until it updates from
    Your side
    """
    myDeal(
        id: ID! @rules(apply: ["exists:\\App\\Models\\UserDeal,id"]) @whereKey
    ): MyDeal!
        @whereAuth(relation: "user")
        @find(model: "App\\Models\\UserDeal")
    #@cache(maxAge: 3600, private: true)

    myDeals: [MyDeal!]
        @paginate(builder: "App\\GraphQL\\Types\\Deal\\MyDeal")
        #@cache(maxAge: 3600, private: true)
        @whereAuth(relation: "user")
}
