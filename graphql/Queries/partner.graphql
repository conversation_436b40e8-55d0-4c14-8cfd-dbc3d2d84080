extend type Query {
    partner(
        id: ID! @rules(apply: ["exists:\\App\\Models\\Partner,id"]) @whereKey
    ): Partner! @find

    partners: [Partner!]! @paginate

    partnerPlace(
        id: ID!
            @rules(apply: ["exists:\\App\\Models\\PartnerLocation,id"])
            @whereKey
    ): PartnerPlace! @find(model: "App\\Models\\PartnerLocation") #@cache(maxAge: 3600)
    partnerPlaces: [PartnerPlace!]!
        @paginate(model: "App\\Models\\PartnerLocation")
}
