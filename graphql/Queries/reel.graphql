extend type Query {
    """
    Get a reel by its ID.
    """
    reel(id: ID! @whereKey): <PERSON><PERSON> @find

    """
    Get a list of reels.
    """
    reels(input: ReelInput): [<PERSON>el]! @paginate

    """
    Get reels meta statuses for multiple reels.
    """
    reelsMetaStatuses(ids: [ID!]!): [Reel!]! @all(scopes: ["byIds"])

    """
    Get reels By Place
    """
    reelsByPlace(placeId: ID!): [Reel!]!
        @paginate(builder: "App\\GraphQL\\Queries\\ReelsByPlaceQuery@resolve")
}

input ReelInput {
    all: Boolean
    by_place: <PERSON><PERSON><PERSON> @whereNull(key: "creatable_type")
    by_creator: <PERSON><PERSON><PERSON> @scope(name: "filterBy<PERSON><PERSON>")
}
