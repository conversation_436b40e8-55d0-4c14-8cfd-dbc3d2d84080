APP_NAME=Laravel
APP_ENV=testing
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=Africa/Cairo
APP_URL=http://localhost:8000
FRONTEND_URL=http://localhost:3000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
DB_DATABASE=":memory:"
#DB_HOST=127.0.0.1
#DB_PORT=3306
#DB_DATABASE=conari
#DB_USERNAME=root
#DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log

FILESYSTEM_DISK=local
FILAMENT_FILESYSTEM_DISK=local
MEDIA_DISK=local

QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log

MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=eu-north-1
AWS_BUCKET=conari-dev
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

#OCTANE_SERVER=swoole

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT=
GOOGLE_ENABLED=false

APPLE_CLIENT_ID=com.conari.app
APPLE_TEAM_ID=
APPLE_KEY_ID=
APPLE_PRIVATE_KEY=path/to/key
APPLE_SHARED_SECRET=
APPLE_ENABLED=true

ALGOLIA_APP_ID=
ALGOLIA_API_KEY=
ALGOLIA_SECRET=

SCOUT_QUEUE=false

LIGHTHOUSE_DEBUG=2

FFMPEG_BINARIES=
FFPROBE_BINARIES=
