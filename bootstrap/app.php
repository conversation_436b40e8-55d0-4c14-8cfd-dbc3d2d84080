<?php

use App\Console\Commands\CreateThumbnailsForReelsCommand;
use App\Console\Commands\Deal\MarkDealsAsNoShowCommand;
use App\Console\Commands\Deal\MarkDealsAsRedeemableCommand;
use App\Console\Commands\Deal\MarkDealsAsUpComingCommand;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Sentry\Laravel\Integration;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withSchedule(function (Schedule $schedule) {
        $schedule->command(MarkDealsAsUpComingCommand::class)
            ->everyMinute();

        $schedule->command(MarkDealsAsNoShowCommand::class)
            ->everyMinute();

        $schedule->command(MarkDealsAsRedeemableCommand::class)
            ->everyMinute();

        $schedule->command(CreateThumbnailsForReelsCommand::class)
            ->everyMinute();
    })
    ->withExceptions(function (Exceptions $exceptions) {
        Integration::handles($exceptions);
    })->create();
